const mysqlConnection = require("mysql2/promise");
const dotenv = require("dotenv");

dotenv.config();

async function main() {
  console.log("Updating posts table with new fields...");

  try {
    // Get database credentials from .env file
    const dbHost = process.env.DATABASE_HOST || "localhost";
    const dbUser = process.env.DATABASE_USERNAME || "root";
    const dbPassword = process.env.DATABASE_PASSWORD || "";
    const dbName = process.env.DATABASE_NAME || "hifnf_db";
    const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

    console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

    // Connect to the database
    const connection = await mysqlConnection.createConnection({
      host: dbH<PERSON>,
      user: dbU<PERSON>,
      password: dbPassword,
      database: dbName,
      port: dbPort,
      multipleStatements: true,
    });

    console.log(`Connected to database '${dbName}'`);

    // Check if the moderation columns already exist
    const [moderationColumns] = await connection.execute(`
      SHOW COLUMNS FROM posts LIKE 'isReported'
    `);

    // @ts-ignore
    if (moderationColumns.length === 0) {
      console.log("Adding moderation columns to posts table...");

      // Add moderation columns to the posts table
      await connection.execute(`
        ALTER TABLE posts
        ADD COLUMN isReported BOOLEAN DEFAULT FALSE,
        ADD COLUMN moderationStatus ENUM('pending', 'approved', 'rejected') DEFAULT 'approved' NOT NULL,
        ADD COLUMN moderatedBy VARCHAR(255),
        ADD COLUMN moderatedAt TIMESTAMP NULL;
      `);

      console.log("Moderation columns added to posts table!");
    } else {
      console.log("Moderation columns already exist in posts table.");
    }

    // Create post_reports table if it doesn't exist
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS post_reports (
        id VARCHAR(255) PRIMARY KEY,
        postId VARCHAR(255) NOT NULL,
        reporterId VARCHAR(255) NOT NULL,
        reason ENUM('spam', 'harassment', 'inappropriate_content', 'violence', 'misinformation', 'other') NOT NULL,
        description TEXT,
        status ENUM('pending', 'reviewed', 'resolved', 'dismissed') NOT NULL DEFAULT 'pending',
        reviewedBy VARCHAR(255),
        reviewedAt TIMESTAMP NULL,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (postId) REFERENCES posts(id) ON DELETE CASCADE,
        FOREIGN KEY (reporterId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (reviewedBy) REFERENCES users(id) ON DELETE SET NULL
      )
    `);
    console.log("Post reports table created/verified!");

    // Close the connection
    await connection.end();
    console.log("Database connection closed.");
  } catch (error) {
    console.error("Error updating posts table:", error);
    process.exit(1);
  }
}

main();
