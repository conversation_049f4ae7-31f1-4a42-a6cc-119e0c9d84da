import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users, posts, subscriptions, blogs, products, stores } from "@/lib/db/schema";
import { eq, or, and, count } from "drizzle-orm";

export async function GET(
  req: Request,
  context: { params: Promise<{ username: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const username = params.username;

    console.log(`Fetching user profile for username: ${username}`);

    // Fetch the user profile from the database by username
    const user = await db.query.users.findFirst({
      where: eq(users.username, username),
      columns: {
        id: true,
        name: true,
        username: true,
        email: true,
        image: true,
        coverImage: true,
        bio: true,
        location: true,
        birthday: true,
        website: true,
        facebook: true,
        twitter: true,
        instagram: true,
        linkedin: true,
        youtube: true,
        work: true,
        education: true,
        createdAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    // Get user statistics
    const isOwnProfile = user.id === session.user.id;

    console.log(`Getting statistics for user: ${user.id}, isOwnProfile: ${isOwnProfile}`);

    // Get statistics with individual error handling
    let postsCount, subscribersCount, blogsCount, productsCount;

    try {
      console.log('Fetching posts count...');
      postsCount = await db.select({ count: count() }).from(posts).where(eq(posts.userId, user.id));
    } catch (error) {
      console.error('Error fetching posts count:', error);
      postsCount = [{ count: 0 }];
    }

    try {
      console.log('Fetching subscribers count...');
      subscribersCount = await db.select({ count: count() }).from(subscriptions).where(eq(subscriptions.targetUserId, user.id));
    } catch (error) {
      console.error('Error fetching subscribers count:', error);
      subscribersCount = [{ count: 0 }];
    }

    try {
      console.log('Fetching blogs count...');
      // Check if blogs table exists first
      const blogTableExists = await db.select({ count: count() }).from(blogs).limit(1).catch(() => null);

      if (blogTableExists !== null) {
        blogsCount = await db.select({ count: count() }).from(blogs).where(
          isOwnProfile
            ? eq(blogs.authorId, user.id)
            : and(eq(blogs.authorId, user.id), eq(blogs.status, "published"))
        );
      } else {
        console.log('Blogs table does not exist, setting count to 0');
        blogsCount = [{ count: 0 }];
      }
    } catch (error) {
      console.error('Error fetching blogs count:', error);
      blogsCount = [{ count: 0 }];
    }

    try {
      console.log('Fetching products count...');
      // Check if stores and products tables exist first
      const storesTableExists = await db.select({ count: count() }).from(stores).limit(1).catch(() => null);
      const productsTableExists = await db.select({ count: count() }).from(products).limit(1).catch(() => null);

      if (storesTableExists !== null && productsTableExists !== null) {
        // First check if user has any stores
        const userStores = await db.select({ id: stores.id }).from(stores).where(eq(stores.ownerId, user.id));

        if (userStores.length > 0) {
          productsCount = await db.select({ count: count() })
            .from(stores)
            .leftJoin(products, eq(products.storeId, stores.id))
            .where(eq(stores.ownerId, user.id));
        } else {
          productsCount = [{ count: 0 }];
        }
      } else {
        console.log('Stores or products table does not exist, setting count to 0');
        productsCount = [{ count: 0 }];
      }
    } catch (error) {
      console.error('Error fetching products count:', error);
      productsCount = [{ count: 0 }];
    }

    // Get photos count (posts with images)
    let photosCount = 0;
    try {
      console.log('Fetching photos count...');
      const allUserPosts = await db
        .select({ images: posts.images })
        .from(posts)
        .where(eq(posts.userId, user.id));

      photosCount = allUserPosts.filter(post =>
        post.images && Array.isArray(post.images) && post.images.length > 0
      ).length;
    } catch (error) {
      console.error('Error fetching photos count:', error);
      photosCount = 0;
    }

    // Create a new user object without email if not the current user
    const userResponse = {
      ...user,
      _count: {
        posts: postsCount[0]?.count || 0,
        subscribers: subscribersCount[0]?.count || 0,
        photos: photosCount || 0,
        blogs: blogsCount[0]?.count || 0,
        products: productsCount[0]?.count || 0,
      }
    };

    // For privacy, only return email if it's the current user
    if (user.id !== session.user.id) {
      // @ts-expect-error - We know email exists on the user object
      userResponse.email = undefined;
    }

    console.log('API response user data by username:', userResponse);
    return NextResponse.json(userResponse);
  } catch (error) {
    console.error("Error fetching user profile by username:", error);

    // Provide more detailed error information for debugging
    let errorMessage = "Internal server error";
    let errorDetails = {};

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = {
        name: error.name,
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      };

      // Check for specific database errors
      if ('code' in error && typeof error.code === 'string') {
        const dbError = error as { code: string; sqlMessage?: string; errno?: number };

        errorDetails = {
          ...errorDetails,
          code: dbError.code,
          errno: dbError.errno,
          sqlMessage: dbError.sqlMessage,
        };

        if (dbError.code === 'ER_NO_SUCH_TABLE') {
          errorMessage = "Database table does not exist. Please run database migrations.";
        } else if (dbError.code === 'ER_BAD_FIELD_ERROR') {
          errorMessage = "Database schema mismatch. Please check your database structure.";
        } else if (dbError.code === 'ECONNREFUSED') {
          errorMessage = "Cannot connect to database. Please check your database configuration.";
        } else if (dbError.code === 'ER_CANT_AGGREGATE_2COLLATIONS') {
          errorMessage = "Database configuration issue. Please contact support.";
        }
      }
    }

    console.error("Detailed error information:", errorDetails);

    return NextResponse.json(
      {
        message: errorMessage,
        error: process.env.NODE_ENV === 'development' ? errorDetails : 'Internal server error'
      },
      { status: 500 }
    );
  }
}
