import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { groups, groupMembers, posts, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and, desc } from "drizzle-orm";

const postSchema = z.object({
  content: z.string().min(1).max(5000),
  images: z.array(z.string().url()).optional(),
  videos: z.array(z.string().url()).optional(),
  backgroundColor: z.string().optional(),
  feeling: z.string().optional(),
  activity: z.string().optional(),
  location: z.string().optional(),
  formattedContent: z.boolean().optional(),
  isAnnouncement: z.boolean().optional(),
});

// Get posts from a group
export async function GET(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the user has access to view posts
    const userMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, session.user.id)
      ),
    });

    const isPublic = group.visibility === "public";
    const isMember = userMembership && (userMembership.role === "admin" || userMembership.role === "moderator" || userMembership.role === "member");

    // For private groups, only members can see the posts
    if (!isPublic && !isMember) {
      return NextResponse.json(
        { message: "You don't have permission to view posts in this group" },
        { status: 403 }
      );
    }

    // Fetch posts from the database
    const groupPosts = await db.query.posts.findMany({
      where: eq(posts.groupId, groupId),
      orderBy: [desc(posts.createdAt)],
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            username: true,
            image: true,
          },
        },
        likes: true,
        comments: {
          columns: {
            id: true,
          },
        },
        sharedPost: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                username: true,
                image: true,
              },
            },
          },
        },
        shares: true,
      },
    });

    // Format posts for universal PostCard interface
    const formattedPosts = await Promise.all(
      groupPosts.map(async (post) => {
        // Check if the current user has liked or disliked this post
        const userLike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'like'
        );

        const userDislike = post.likes.find(like =>
          like.userId === session.user.id && like.type === 'dislike'
        );

        const likesCount = post.likes.filter(like => like.type === 'like').length;
        const dislikesCount = post.likes.filter(like => like.type === 'dislike').length;

        return {
          id: post.id,
          content: post.content,
          images: post.images,
          videos: post.videos,
          privacy: post.privacy,
          backgroundColor: post.backgroundColor,
          feeling: post.feeling,
          activity: post.activity,
          location: post.location,
          formattedContent: post.formattedContent,
          createdAt: post.createdAt.toISOString(),
          type: 'group_post' as const,
          user: post.user,
          fanPage: null,
          group: {
            id: group.id,
            name: group.name,
            slug: group.slug,
            profileImage: group.profileImage,
            isPrivate: group.isPrivate
          },
          _count: {
            likes: likesCount,
            dislikes: dislikesCount,
            comments: post.comments.length,
            shares: post.shares.length,
          },
          liked: !!userLike,
          disliked: !!userDislike,
          sharedPost: post.sharedPost ? {
            id: post.sharedPost.id,
            content: post.sharedPost.content,
            images: post.sharedPost.images,
            videos: post.sharedPost.videos,
            backgroundColor: post.sharedPost.backgroundColor,
            feeling: post.sharedPost.feeling,
            activity: post.sharedPost.activity,
            location: post.sharedPost.location,
            formattedContent: post.sharedPost.formattedContent,
            createdAt: post.sharedPost.createdAt.toISOString(),
            user: post.sharedPost.user,
          } : null,
        };
      })
    );

    return NextResponse.json(formattedPosts);
  } catch (error) {
    console.error("Error fetching group posts:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create a post in a group
export async function POST(
  req: Request,
  context: { params: Promise<{ groupId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const { groupId } = params;
    const body = await req.json();
    const validatedData = postSchema.parse(body);

    // Check if the group exists
    const group = await db.query.groups.findFirst({
      where: eq(groups.id, groupId),
    });

    if (!group) {
      return NextResponse.json(
        { message: "Group not found" },
        { status: 404 }
      );
    }

    // Check if the user is a member of the group
    const userMembership = await db.query.groupMembers.findFirst({
      where: and(
        eq(groupMembers.groupId, groupId),
        eq(groupMembers.userId, session.user.id)
      ),
    });

    const isAdmin = userMembership && userMembership.role === "admin";
    const isModerator = userMembership && userMembership.role === "moderator";
    const isMember = userMembership && userMembership.role === "member";
    const isCreator = group.creatorId === session.user.id;

    // Check if the user has permission to post
    if (!isAdmin && !isModerator && !isMember && !isCreator) {
      return NextResponse.json(
        { message: "You must be a member to post in this group" },
        { status: 403 }
      );
    }

    // Check if this is an announcement (only admins and moderators can make announcements)
    const isAnnouncement = validatedData.isAnnouncement || false;
    if (isAnnouncement && !isAdmin && !isModerator && !isCreator) {
      return NextResponse.json(
        { message: "Only admins and moderators can make announcements" },
        { status: 403 }
      );
    }

    // Check posting permissions
    if (group.postPermission === "admin-only" && !isAdmin && !isCreator) {
      return NextResponse.json(
        { message: "Only admins can post in this group" },
        { status: 403 }
      );
    }

    const postId = uuidv4();

    // Insert the post into the database
    await db.insert(posts).values({
      id: postId,
      userId: session.user.id,
      groupId,
      content: validatedData.content,
      images: validatedData.images || null,
      videos: validatedData.videos || null,
      backgroundColor: validatedData.backgroundColor || null,
      feeling: validatedData.feeling || null,
      activity: validatedData.activity || null,
      location: validatedData.location || null,
      formattedContent: validatedData.formattedContent || false,
    });

    // If this is an announcement, create notifications for all group members
    if (isAnnouncement) {
      // Get all members of the group
      const members = await db.query.groupMembers.findMany({
        where: and(
          eq(groupMembers.groupId, groupId),
          eq(groupMembers.role, "member")
        ),
      });

      // Create a notification for each member
      for (const member of members) {
        if (member.userId !== session.user.id) { // Don't notify the poster
          const notificationId = uuidv4();
          await db.insert(notifications).values({
            id: notificationId,
            recipientId: member.userId,
            type: "group_announcement",
            senderId: session.user.id,
            groupId,
            postId,
          });
        }
      }
    }

    return NextResponse.json(
      { message: "Post created successfully", id: postId },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating group post:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Validation error", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
