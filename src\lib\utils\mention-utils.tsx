import Link from "next/link";

/**
 * Renders text with @mentions as clickable links
 * @param text - The text content that may contain @mentions
 * @returns JSX element with mentions highlighted and linked
 */
export function renderTextWithMentions(text: string) {
  if (!text) return text;

  // Regular expression to match @mentions (username or name with spaces)
  const mentionRegex = /@([a-zA-Z0-9_]+(?:\s+[a-zA-Z0-9_]+)*)/g;

  const parts: (string | JSX.Element)[] = [];
  let lastIndex = 0;
  let match;

  while ((match = mentionRegex.exec(text)) !== null) {
    // Add text before the mention
    if (match.index > lastIndex) {
      parts.push(text.slice(lastIndex, match.index));
    }

    // Add the mention as a clickable link
    const mentionText = match[1].trim();
    parts.push(
      <Link
        key={`mention-${match.index}-${mentionText}`}
        href={`/user/${encodeURIComponent(mentionText)}`}
        className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer transition-colors duration-200"
        onClick={(e) => {
          e.stopPropagation(); // Prevent event bubbling
        }}
      >
        @{mentionText}
      </Link>
    );

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text after the last mention
  if (lastIndex < text.length) {
    parts.push(text.slice(lastIndex));
  }

  return parts.length > 1 ? <>{parts}</> : text;
}

/**
 * Extracts mentioned usernames from text
 * @param text - The text content that may contain @mentions
 * @returns Array of mentioned usernames
 */
export function extractMentions(text: string): string[] {
  if (!text) return [];

  const mentionRegex = /@([a-zA-Z0-9_]+(?:\s+[a-zA-Z0-9_]+)*)/g;
  const mentions: string[] = [];
  let match;

  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push(match[1].trim());
  }

  return mentions;
}
