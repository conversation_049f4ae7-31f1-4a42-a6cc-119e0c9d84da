"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/Button";
import { UsersIcon } from "@heroicons/react/24/outline";

interface GroupJoinButtonProps {
  groupId: string;
  isMember: boolean;
  isPending: boolean;
  visibility: "public" | "private-visible" | "private-hidden";
}

export function GroupJoinButton({
  groupId,
  isMember,
  isPending,
  visibility,
}: GroupJoinButtonProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleJoinGroup = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/groups/${groupId}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: "current", // The API will use the current user's ID
          action: "request",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to join group");
      }

      // Refresh the page to show updated status
      router.refresh();
    } catch (error) {
      console.error("Error joining group:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLeaveGroup = async () => {
    if (!confirm("Are you sure you want to leave this group?")) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/groups/${groupId}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: "current", // The API will use the current user's ID
          action: "remove",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to leave group");
      }

      // Refresh the page to show updated status
      router.refresh();
    } catch (error) {
      console.error("Error leaving group:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  if (isMember) {
    return (
      <Button
        variant="outline"
        onClick={handleLeaveGroup}
        isLoading={isLoading}
        disabled={isLoading}
      >
        {!isLoading && <UsersIcon className="mr-1 h-5 w-5" />}
        Leave Group
      </Button>
    );
  }

  if (isPending) {
    return (
      <Button variant="outline" disabled>
        Request Pending
      </Button>
    );
  }

  return (
    <Button
      onClick={handleJoinGroup}
      isLoading={isLoading}
      disabled={isLoading}
    >
      {!isLoading && <UsersIcon className="mr-1 h-5 w-5" />}
      {visibility === "public" ? "Join Group" : "Request to Join"}
    </Button>
  );
}
