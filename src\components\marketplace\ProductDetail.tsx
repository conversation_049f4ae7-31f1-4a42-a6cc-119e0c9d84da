"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { formatDistanceToNow, format } from "date-fns";
import {
  BuildingStorefrontIcon,
  CheckBadgeIcon,
  MapPinIcon,
  CalendarIcon,
  EyeIcon,
  FlagIcon,
  ChatBubbleLeftRightIcon,
  PencilIcon,
  TrashIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import { ReportForm } from "@/components/marketplace/ReportForm";

interface ProductDetailProps {
  product: {
    id: string;
    title: string;
    description: string | null;
    price: number;
    condition: string;
    category: string;
    location: string | null;
    photos: string[] | null;
    viewCount: number | null;
    createdAt: Date;
    updatedAt: Date;
    store: {
      id: string;
      name: string;
      slug: string;
      logo: string | null;
      isVerified: boolean | null;
    } | null;
    owner: {
      id: string;
      name: string | null;
      image: string | null;
    } | null;
  };
}

export function ProductDetail({ product }: ProductDetailProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [showReportForm, setShowReportForm] = useState(false);

  const isOwner = session?.user?.id === product.owner?.id;

  // Format condition for display
  const formatCondition = (condition: string | null | undefined) => {
    if (!condition) return 'Unknown';
    return condition.replace('_', ' ').replace(/\b\w/g, (c) => c.toUpperCase());
  };

  // Format price to currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price); // Price is already in correct format
  };

  // Handle photo navigation
  const nextPhoto = () => {
    if (product.photos && currentPhotoIndex < product.photos.length - 1) {
      setCurrentPhotoIndex(currentPhotoIndex + 1);
    }
  };

  const prevPhoto = () => {
    if (currentPhotoIndex > 0) {
      setCurrentPhotoIndex(currentPhotoIndex - 1);
    }
  };

  // Handle contact seller
  const contactSeller = () => {
    // In a real app, this would open a chat or messaging interface
    alert("This would open a messaging interface to contact the seller.");
  };

  // Handle delete product
  const deleteProduct = async () => {
    if (!confirm("Are you sure you want to delete this product?")) {
      return;
    }

    try {
      const response = await fetch(`/api/marketplace/products/${product.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete product");
      }

      router.push("/my-store/dashboard");
      router.refresh();
    } catch (error) {
      console.error("Error deleting product:", error);
      alert("Failed to delete product. Please try again.");
    }
  };

  return (
    <div className="space-y-8">
      {/* Back button */}
      <div>
        <button
          onClick={() => router.back()}
          className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
        >
          <ChevronLeftIcon className="mr-1 h-4 w-4" />
          Back to Marketplace
        </button>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Product Images */}
        <div className="space-y-4">
          <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
            {product.photos && product.photos.length > 0 ? (
              <>
                <OptimizedImage
                  src={product.photos[currentPhotoIndex]}
                  alt={product.title}
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  className="object-contain"
                />

                {product.photos.length > 1 && (
                  <>
                    <button
                      onClick={prevPhoto}
                      disabled={currentPhotoIndex === 0}
                      className="absolute left-2 top-1/2 -translate-y-1/2 rounded-full bg-white p-2 shadow-md disabled:opacity-50"
                    >
                      <ChevronLeftIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={nextPhoto}
                      disabled={currentPhotoIndex === product.photos.length - 1}
                      className="absolute right-2 top-1/2 -translate-y-1/2 rounded-full bg-white p-2 shadow-md disabled:opacity-50"
                    >
                      <ChevronRightIcon className="h-5 w-5" />
                    </button>
                  </>
                )}
              </>
            ) : (
              <div className="flex h-full w-full items-center justify-center">
                <span className="text-gray-400">No image available</span>
              </div>
            )}
          </div>

          {/* Thumbnail navigation */}
          {product.photos && product.photos.length > 1 && (
            <div className="flex space-x-2 overflow-x-auto">
              {product.photos.map((photo, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPhotoIndex(index)}
                  className={`relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border-2 ${
                    index === currentPhotoIndex ? "border-blue-500" : "border-gray-200"
                  }`}
                >
                  <OptimizedImage
                    src={photo}
                    alt={`${product.title} - photo ${index + 1}`}
                    fill
                    sizes="64px"
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl">
              {product.title}
            </h1>
            <p className="mt-2 text-3xl font-bold text-blue-600">
              {formatPrice(product.price)}
            </p>
          </div>

          <div className="flex flex-wrap gap-4 text-sm text-gray-500">
            <div className="flex items-center">
              <span className="mr-2 rounded-full bg-gray-100 px-3 py-1">
                {formatCondition(product.condition)}
              </span>
            </div>

            <div className="flex items-center">
              <EyeIcon className="mr-1 h-4 w-4" />
              <span>{product.viewCount || 0} views</span>
            </div>

            {product.category && (
              <div className="flex items-center">
                <span className="rounded-full bg-blue-100 px-3 py-1 text-blue-800">
                  {product.category}
                </span>
              </div>
            )}
          </div>

          {product.description && (
            <div>
              <h2 className="mb-2 text-lg font-medium text-gray-900">Description</h2>
              <p className="whitespace-pre-line text-gray-600">{product.description}</p>
            </div>
          )}

          <div className="flex flex-wrap gap-4 text-sm text-gray-500">
            {product.location && (
              <div className="flex items-center">
                <MapPinIcon className="mr-1 h-4 w-4" />
                <span>{product.location}</span>
              </div>
            )}

            <div className="flex items-center">
              <CalendarIcon className="mr-1 h-4 w-4" />
              <span>Posted {formatDistanceToNow(new Date(product.createdAt), { addSuffix: true })}</span>
            </div>
          </div>

          {/* Store info */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="mr-3 h-10 w-10 overflow-hidden rounded-full">
                {product.store?.logo ? (
                  <OptimizedImage
                    src={product.store.logo}
                    alt={product.store.name}
                    width={40}
                    height={40}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-blue-100">
                    <span className="text-lg font-semibold text-blue-500">
                      {product.store?.name.charAt(0).toUpperCase() || "S"}
                    </span>
                  </div>
                )}
              </div>

              <div>
                <div className="flex items-center">
                  <Link
                    href={`/store/${product.store?.slug || "unknown"}`}
                    className="font-medium text-gray-900 hover:text-blue-600"
                  >
                    {product.store?.name || "Unknown Store"}
                  </Link>
                  {product.store?.isVerified === true && (
                    <CheckBadgeIcon className="ml-1 h-4 w-4 text-blue-500" title="Verified Store" />
                  )}
                </div>
                <p className="text-sm text-gray-500">by {product.owner?.name || "Unknown Seller"}</p>
              </div>
            </div>

            <div className="mt-4 flex space-x-2">
              <Link href={`/store/${product.store?.slug || "unknown"}`} className="flex-1">
                <Button variant="outline" className="w-full">
                  <BuildingStorefrontIcon className="mr-1 h-4 w-4" />
                  View Store
                </Button>
              </Link>

              {!isOwner && (
                <Button className="flex-1" onClick={contactSeller}>
                  <ChatBubbleLeftRightIcon className="mr-1 h-4 w-4" />
                  Contact Seller
                </Button>
              )}
            </div>
          </div>

          {/* Owner actions */}
          {isOwner && (
            <div className="flex space-x-2">
              <Link href={`/marketplace/product/${product.id}/edit`} className="flex-1">
                <Button variant="outline" className="w-full">
                  <PencilIcon className="mr-1 h-4 w-4" />
                  Edit Product
                </Button>
              </Link>

              <Button
                variant="outline"
                className="flex-1 text-red-600 hover:bg-red-50 hover:text-red-700"
                onClick={deleteProduct}
              >
                <TrashIcon className="mr-1 h-4 w-4" />
                Delete Product
              </Button>
            </div>
          )}

          {/* Report button */}
          {!isOwner && (
            <button
              onClick={() => setShowReportForm(true)}
              className="mt-2 inline-flex items-center text-sm text-gray-500 hover:text-red-600"
            >
              <FlagIcon className="mr-1 h-4 w-4" />
              Report this listing
            </button>
          )}
        </div>
      </div>

      {/* Report form modal */}
      {showReportForm && (
        <ReportForm
          productId={product.id}
          onClose={() => setShowReportForm(false)}
        />
      )}
    </div>
  );
}
