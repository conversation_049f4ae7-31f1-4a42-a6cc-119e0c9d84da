import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { blogLikes, blogDislikes, users, blogs } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    slug: string;
  }>;
}

// GET /api/blogs/[slug]/reactions - Get users who reacted to a blog post
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { slug } = await params;

    // Verify blog exists
    const blog = await db.query.blogs.findFirst({
      where: eq(blogs.slug, slug),
    });

    if (!blog) {
      return NextResponse.json(
        { message: "Blog not found" },
        { status: 404 }
      );
    }

    // Fetch users who liked the blog
    const likesData = await db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
        createdAt: blogLikes.createdAt,
      })
      .from(blogLikes)
      .innerJoin(users, eq(blogLikes.userId, users.id))
      .where(eq(blogLikes.blogId, blog.id))
      .orderBy(desc(blogLikes.createdAt));

    // Fetch users who disliked the blog
    const dislikesData = await db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
        createdAt: blogDislikes.createdAt,
      })
      .from(blogDislikes)
      .innerJoin(users, eq(blogDislikes.userId, users.id))
      .where(eq(blogDislikes.blogId, blog.id))
      .orderBy(desc(blogDislikes.createdAt));

    // Format the data
    const likes = likesData.map(user => ({
      ...user,
      reactionType: 'like' as const,
    }));

    const dislikes = dislikesData.map(user => ({
      ...user,
      reactionType: 'dislike' as const,
    }));

    return NextResponse.json({
      likes,
      dislikes,
      total: {
        likes: likes.length,
        dislikes: dislikes.length,
      }
    });

  } catch (error) {
    console.error("Error fetching blog reactions:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
