import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { likes, users, posts } from "@/lib/db/schema";
import { eq, desc, and } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    postId: string;
  }>;
}

// GET /api/posts/[postId]/reactions - Get users who reacted to a post
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { postId } = await params;

    // Verify post exists
    const post = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Fetch users who liked the post
    const likesData = await db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
        createdAt: likes.createdAt,
      })
      .from(likes)
      .innerJoin(users, eq(likes.userId, users.id))
      .where(and(eq(likes.postId, postId), eq(likes.type, 'like')))
      .orderBy(desc(likes.createdAt));

    // Fetch users who disliked the post
    const dislikesData = await db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
        createdAt: likes.createdAt,
      })
      .from(likes)
      .innerJoin(users, eq(likes.userId, users.id))
      .where(and(eq(likes.postId, postId), eq(likes.type, 'dislike')))
      .orderBy(desc(likes.createdAt));

    // Format the data
    const likes = likesData.map(user => ({
      ...user,
      reactionType: 'like' as const,
    }));

    const dislikes = dislikesData.map(user => ({
      ...user,
      reactionType: 'dislike' as const,
    }));

    return NextResponse.json({
      likes,
      dislikes,
      total: {
        likes: likes.length,
        dislikes: dislikes.length,
      }
    });

  } catch (error) {
    console.error("Error fetching post reactions:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
