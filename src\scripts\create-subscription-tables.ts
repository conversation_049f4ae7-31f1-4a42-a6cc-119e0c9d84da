import mysql from 'mysql2/promise';
import { config } from 'dotenv';

// Load environment variables
config();

async function createSubscriptionTables() {
  let connection: mysql.Connection | null = null;

  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST || 'localhost',
      user: process.env.DATABASE_USERNAME || 'root',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || 'hifnf',
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('Connected to database');

    // Create subscription_plans table
    console.log('Creating subscription_plans table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS subscription_plans (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        displayName VARCHAR(100) NOT NULL,
        description TEXT,
        price DECIMAL(10, 2) NOT NULL,
        currency VARCHAR(10) DEFAULT 'USD' NOT NULL,
        billingCycle ENUM('monthly', 'yearly') DEFAULT 'monthly' NOT NULL,
        features JSON,
        maxPosts INT DEFAULT -1,
        maxStorage INT DEFAULT -1,
        maxGroups INT DEFAULT -1,
        canCreateFanPages BOOLEAN DEFAULT FALSE,
        canCreateStores BOOLEAN DEFAULT FALSE,
        canMonetizeBlogs BOOLEAN DEFAULT FALSE,
        prioritySupport BOOLEAN DEFAULT FALSE,
        isActive BOOLEAN DEFAULT TRUE NOT NULL,
        sortOrder INT DEFAULT 0,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ subscription_plans table created');

    // Create user_subscriptions table
    console.log('Creating user_subscriptions table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_subscriptions (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL,
        planId VARCHAR(255) NOT NULL,
        status ENUM('active', 'cancelled', 'expired', 'pending', 'suspended') DEFAULT 'pending' NOT NULL,
        startDate TIMESTAMP NOT NULL,
        endDate TIMESTAMP NOT NULL,
        nextBillingDate TIMESTAMP,
        cancelledAt TIMESTAMP,
        cancelReason TEXT,
        autoRenew BOOLEAN DEFAULT TRUE NOT NULL,
        paymentMethod VARCHAR(50),
        lastPaymentDate TIMESTAMP,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (planId) REFERENCES subscription_plans(id) ON DELETE RESTRICT
      )
    `);
    console.log('✅ user_subscriptions table created');

    // Create subscription_transactions table
    console.log('Creating subscription_transactions table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS subscription_transactions (
        id VARCHAR(255) PRIMARY KEY,
        subscriptionId VARCHAR(255) NOT NULL,
        userId VARCHAR(255) NOT NULL,
        planId VARCHAR(255) NOT NULL,
        type ENUM('payment', 'refund', 'upgrade', 'downgrade', 'cancellation') NOT NULL,
        amount DECIMAL(10, 2) NOT NULL,
        currency VARCHAR(10) DEFAULT 'USD' NOT NULL,
        status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending' NOT NULL,
        paymentGateway VARCHAR(50),
        gatewayTransactionId VARCHAR(255),
        gatewayResponse JSON,
        description TEXT,
        metadata JSON,
        processedAt TIMESTAMP,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (subscriptionId) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (planId) REFERENCES subscription_plans(id) ON DELETE RESTRICT
      )
    `);
    console.log('✅ subscription_transactions table created');

    // Insert default subscription plans
    console.log('Inserting default subscription plans...');
    
    const plans = [
      {
        id: 'free-plan',
        name: 'free',
        displayName: 'Free',
        description: 'Basic features with limited access',
        price: '0.00',
        currency: 'USD',
        billingCycle: 'monthly',
        features: JSON.stringify([
          'Create up to 10 posts per month',
          'Join unlimited groups',
          'Basic messaging',
          '100MB storage',
          'Community support'
        ]),
        maxPosts: 10,
        maxStorage: 100,
        maxGroups: -1,
        canCreateFanPages: false,
        canCreateStores: false,
        canMonetizeBlogs: false,
        prioritySupport: false,
        sortOrder: 1
      },
      {
        id: 'basic-plan',
        name: 'basic',
        displayName: 'Basic',
        description: 'Perfect for casual users who want more features',
        price: '9.99',
        currency: 'USD',
        billingCycle: 'monthly',
        features: JSON.stringify([
          'Create up to 100 posts per month',
          'Create up to 3 groups',
          'Advanced messaging',
          '1GB storage',
          'Email support',
          'Ad-free experience'
        ]),
        maxPosts: 100,
        maxStorage: 1024,
        maxGroups: 3,
        canCreateFanPages: false,
        canCreateStores: false,
        canMonetizeBlogs: false,
        prioritySupport: false,
        sortOrder: 2
      },
      {
        id: 'premium-plan',
        name: 'premium',
        displayName: 'Premium',
        description: 'For power users who need advanced features',
        price: '19.99',
        currency: 'USD',
        billingCycle: 'monthly',
        features: JSON.stringify([
          'Unlimited posts',
          'Create unlimited groups',
          'Create fan pages',
          'Advanced analytics',
          '10GB storage',
          'Priority support',
          'Blog monetization',
          'Custom themes'
        ]),
        maxPosts: -1,
        maxStorage: 10240,
        maxGroups: -1,
        canCreateFanPages: true,
        canCreateStores: false,
        canMonetizeBlogs: true,
        prioritySupport: true,
        sortOrder: 3
      },
      {
        id: 'pro-plan',
        name: 'pro',
        displayName: 'Pro',
        description: 'Complete solution for businesses and creators',
        price: '39.99',
        currency: 'USD',
        billingCycle: 'monthly',
        features: JSON.stringify([
          'Everything in Premium',
          'Create marketplace stores',
          'Advanced e-commerce tools',
          'White-label options',
          '100GB storage',
          'Dedicated support',
          'API access',
          'Custom integrations'
        ]),
        maxPosts: -1,
        maxStorage: 102400,
        maxGroups: -1,
        canCreateFanPages: true,
        canCreateStores: true,
        canMonetizeBlogs: true,
        prioritySupport: true,
        sortOrder: 4
      }
    ];

    for (const plan of plans) {
      await connection.execute(`
        INSERT IGNORE INTO subscription_plans (
          id, name, displayName, description, price, currency, billingCycle,
          features, maxPosts, maxStorage, maxGroups, canCreateFanPages,
          canCreateStores, canMonetizeBlogs, prioritySupport, sortOrder
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        plan.id, plan.name, plan.displayName, plan.description, plan.price,
        plan.currency, plan.billingCycle, plan.features, plan.maxPosts,
        plan.maxStorage, plan.maxGroups, plan.canCreateFanPages,
        plan.canCreateStores, plan.canMonetizeBlogs, plan.prioritySupport,
        plan.sortOrder
      ]);
    }
    console.log('✅ Default subscription plans inserted');

    console.log('🎉 All subscription tables created successfully!');

  } catch (error) {
    console.error('❌ Error creating subscription tables:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
createSubscriptionTables();
