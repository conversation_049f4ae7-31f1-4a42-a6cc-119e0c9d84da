import { createConnection } from 'mysql2/promise';
import { dbConfig } from '../lib/config';

async function main() {
  console.log("Setting up store_settings table...");
  
  // Create connection
  const connection = await createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });
  
  console.log("Connected to database successfully!");
  
  // Check if store_settings table exists
  const [storeSettingsTable] = await connection.execute(`
    SHOW TABLES LIKE 'store_settings'
  `);
  
  // @ts-ignore
  if (storeSettingsTable.length > 0) {
    console.log("Store settings table already exists in database.");
  } else {
    console.log("Creating store_settings table...");
    
    // Create store_settings table
    await connection.execute(`
      CREATE TABLE store_settings (
        id VARCHAR(255) PRIMARY KEY,
        storeId VARCHAR(255) NOT NULL UNIQUE,
        visibility ENUM('public', 'private') NOT NULL DEFAULT 'public',
        showOutOfStock BOOLEAN DEFAULT TRUE,
        showProductViews BOOLEAN DEFAULT TRUE,
        emailNotifications BOOLEAN DEFAULT TRUE,
        productViewNotifications BOOLEAN DEFAULT FALSE,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE
      )
    `);
    
    console.log("Store settings table created successfully!");
  }
  
  await connection.end();
  console.log("Database connection closed.");
}

main().catch((err) => {
  console.error("Error setting up store_settings table:", err);
  process.exit(1);
});
