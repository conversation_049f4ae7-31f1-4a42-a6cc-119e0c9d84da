import mysql from "mysql2/promise";
import * as dotenv from "dotenv";

dotenv.config();

async function main() {
  console.log("Setting up database...");

  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || "localhost",
    user: process.env.DATABASE_USERNAME || "root",
    password: process.env.DATABASE_PASSWORD || "",
    multipleStatements: true,
  });

  // Create database if it doesn't exist
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  await connection.query(`CREATE DATABASE IF NOT EXISTS \`${dbName}\``);
  console.log(`Database '${dbName}' created or already exists.`);

  // Use the database
  await connection.query(`USE \`${dbName}\``);

  // Create tables
  console.log("Creating tables...");

  // Create users table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS users (
      id VARCHAR(255) PRIMARY KEY,
      name VARCHAR(255),
      email VARCHAR(255) NOT NULL UNIQUE,
      emailVerified TIMESTAMP NULL,
      image VARCHAR(255),
      coverImage VARCHAR(255),
      bio TEXT,
      location VARCHAR(255),
      birthday DATETIME,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `);

  // Create accounts table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS accounts (
      id VARCHAR(255) PRIMARY KEY,
      userId VARCHAR(255) NOT NULL,
      type VARCHAR(255) NOT NULL,
      provider VARCHAR(255) NOT NULL,
      providerAccountId VARCHAR(255) NOT NULL,
      refresh_token TEXT,
      access_token TEXT,
      expires_at INT,
      token_type VARCHAR(255),
      scope VARCHAR(255),
      id_token TEXT,
      session_state VARCHAR(255),
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // Create sessions table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS sessions (
      id VARCHAR(255) PRIMARY KEY,
      sessionToken VARCHAR(255) NOT NULL UNIQUE,
      userId VARCHAR(255) NOT NULL,
      expires TIMESTAMP NOT NULL,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // Create verification tokens table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS verificationTokens (
      identifier VARCHAR(255) NOT NULL,
      token VARCHAR(255) NOT NULL,
      expires TIMESTAMP NOT NULL,
      PRIMARY KEY (identifier, token)
    )
  `);

  // Create posts table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS posts (
      id VARCHAR(255) PRIMARY KEY,
      userId VARCHAR(255) NOT NULL,
      content TEXT,
      images JSON,
      videos JSON,
      privacy ENUM('public', 'friends', 'private') NOT NULL DEFAULT 'public',
      sharedPostId VARCHAR(255),
      backgroundColor VARCHAR(50),
      feeling VARCHAR(100),
      activity VARCHAR(100),
      location VARCHAR(255),
      formattedContent BOOLEAN DEFAULT FALSE,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (sharedPostId) REFERENCES posts(id) ON DELETE SET NULL
    )
  `);

  // Create comments table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS comments (
      id VARCHAR(255) PRIMARY KEY,
      content TEXT NOT NULL,
      userId VARCHAR(255) NOT NULL,
      postId VARCHAR(255) NOT NULL,
      parentId VARCHAR(255),
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (postId) REFERENCES posts(id) ON DELETE CASCADE,
      FOREIGN KEY (parentId) REFERENCES comments(id) ON DELETE SET NULL
    )
  `);

  // Create likes table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS likes (
      id VARCHAR(255) PRIMARY KEY,
      userId VARCHAR(255) NOT NULL,
      postId VARCHAR(255),
      commentId VARCHAR(255),
      type ENUM('like', 'dislike') NOT NULL DEFAULT 'like',
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (postId) REFERENCES posts(id) ON DELETE CASCADE,
      FOREIGN KEY (commentId) REFERENCES comments(id) ON DELETE CASCADE
    )
  `);

  // Create friendships table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS friendships (
      id VARCHAR(255) PRIMARY KEY,
      user1Id VARCHAR(255) NOT NULL,
      user2Id VARCHAR(255) NOT NULL,
      status ENUM('pending', 'accepted', 'rejected', 'blocked') NOT NULL DEFAULT 'pending',
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (user1Id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (user2Id) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // Create messages table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS messages (
      id VARCHAR(255) PRIMARY KEY,
      senderId VARCHAR(255) NOT NULL,
      receiverId VARCHAR(255) NOT NULL,
      content TEXT NOT NULL,
      \`read\` BOOLEAN NOT NULL DEFAULT FALSE,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (senderId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (receiverId) REFERENCES users(id) ON DELETE CASCADE
    )
  `);

  // Create notifications table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS notifications (
      id VARCHAR(255) PRIMARY KEY,
      recipientId VARCHAR(255) NOT NULL,
      type ENUM('like', 'comment', 'friend_request', 'friend_accept', 'message') NOT NULL,
      senderId VARCHAR(255),
      postId VARCHAR(255),
      commentId VARCHAR(255),
      messageId VARCHAR(255),
      friendshipId VARCHAR(255),
      \`read\` BOOLEAN NOT NULL DEFAULT FALSE,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (recipientId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (senderId) REFERENCES users(id) ON DELETE SET NULL,
      FOREIGN KEY (postId) REFERENCES posts(id) ON DELETE SET NULL,
      FOREIGN KEY (commentId) REFERENCES comments(id) ON DELETE SET NULL,
      FOREIGN KEY (messageId) REFERENCES messages(id) ON DELETE SET NULL,
      FOREIGN KEY (friendshipId) REFERENCES friendships(id) ON DELETE SET NULL
    )
  `);

  // Create saved posts table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS savedPosts (
      id VARCHAR(255) PRIMARY KEY,
      userId VARCHAR(255) NOT NULL,
      postId VARCHAR(255) NOT NULL,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (postId) REFERENCES posts(id) ON DELETE CASCADE
    )
  `);

  // Create blog categories table (no foreign keys)
  await connection.query(`
    CREATE TABLE IF NOT EXISTS blogCategories (
      id VARCHAR(255) PRIMARY KEY,
      name VARCHAR(100) NOT NULL,
      slug VARCHAR(100) UNIQUE NOT NULL,
      description TEXT,
      color VARCHAR(7) DEFAULT '#3b82f6',
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create blogs table (without foreign keys first)
  await connection.query(`
    CREATE TABLE IF NOT EXISTS blogs (
      id VARCHAR(255) PRIMARY KEY,
      title VARCHAR(500) NOT NULL,
      slug VARCHAR(500) UNIQUE NOT NULL,
      excerpt TEXT,
      content TEXT NOT NULL,
      coverImage VARCHAR(255),
      authorId VARCHAR(255) NOT NULL,
      categoryId VARCHAR(255),
      tags JSON,
      status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft',
      readTime INT,
      viewCount INT DEFAULT 0,
      featured BOOLEAN DEFAULT FALSE,
      seoTitle VARCHAR(255),
      seoDescription TEXT,
      publishedAt TIMESTAMP,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `);

  // Create blog comments table (without foreign keys first)
  await connection.query(`
    CREATE TABLE IF NOT EXISTS blogComments (
      id VARCHAR(255) PRIMARY KEY,
      blogId VARCHAR(255) NOT NULL,
      userId VARCHAR(255) NOT NULL,
      content TEXT NOT NULL,
      parentId VARCHAR(255),
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `);

  // Create blog monetization table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS blog_monetization (
      id VARCHAR(255) PRIMARY KEY,
      blogId VARCHAR(255) NOT NULL,
      isEnabled BOOLEAN DEFAULT FALSE,
      isApproved BOOLEAN DEFAULT FALSE,
      approvedAt TIMESTAMP NULL,
      approvedBy VARCHAR(255),
      rejectedAt TIMESTAMP NULL,
      rejectedBy VARCHAR(255),
      rejectionReason TEXT,
      cprRate DECIMAL(10,4) DEFAULT 0.0000,
      totalReads INT DEFAULT 0,
      uniqueReads INT DEFAULT 0,
      totalEarnings DECIMAL(15,2) DEFAULT 0.00,
      lastPayoutAt TIMESTAMP NULL,
      status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `);

  // Create blog reads table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS blog_reads (
      id VARCHAR(255) PRIMARY KEY,
      blogId VARCHAR(255) NOT NULL,
      userId VARCHAR(255),
      ipAddress VARCHAR(45) NOT NULL,
      userAgent TEXT,
      readDuration INT DEFAULT 0,
      isUnique BOOLEAN DEFAULT TRUE,
      isQualified BOOLEAN DEFAULT FALSE,
      sessionId VARCHAR(255),
      referrer VARCHAR(500),
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create blog earnings table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS blog_earnings (
      id VARCHAR(255) PRIMARY KEY,
      blogId VARCHAR(255) NOT NULL,
      authorId VARCHAR(255) NOT NULL,
      readCount INT NOT NULL,
      qualifiedReads INT NOT NULL,
      cprRate DECIMAL(10,4) NOT NULL,
      earningAmount DECIMAL(15,2) NOT NULL,
      status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
      paidAt TIMESTAMP NULL,
      transactionId VARCHAR(255),
      periodStart TIMESTAMP NOT NULL,
      periodEnd TIMESTAMP NOT NULL,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create monetization settings table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS monetization_settings (
      id VARCHAR(255) PRIMARY KEY,
      settingKey VARCHAR(100) UNIQUE NOT NULL,
      settingValue TEXT NOT NULL,
      dataType ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
      description TEXT,
      isActive BOOLEAN DEFAULT TRUE,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )
  `);

  // Insert default monetization settings
  await connection.query(`
    INSERT IGNORE INTO monetization_settings (id, settingKey, settingValue, dataType, description) VALUES
    (UUID(), 'cpr_rate', '1.0', 'number', 'Cost per 1000 reads in USD'),
    (UUID(), 'min_payout_threshold', '10.0', 'number', 'Minimum payout threshold in USD'),
    (UUID(), 'min_read_duration', '120', 'number', 'Minimum read duration in seconds'),
    (UUID(), 'monetization_enabled', 'true', 'boolean', 'Enable/disable blog monetization system')
  `);

  // Create blog likes table (without foreign keys first)
  await connection.query(`
    CREATE TABLE IF NOT EXISTS blogLikes (
      id VARCHAR(255) PRIMARY KEY,
      blogId VARCHAR(255) NOT NULL,
      userId VARCHAR(255) NOT NULL,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY unique_blog_like (blogId, userId)
    )
  `);

  // Create blog bookmarks table (without foreign keys first)
  await connection.query(`
    CREATE TABLE IF NOT EXISTS blogBookmarks (
      id VARCHAR(255) PRIMARY KEY,
      blogId VARCHAR(255) NOT NULL,
      userId VARCHAR(255) NOT NULL,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY unique_blog_bookmark (blogId, userId)
    )
  `);

  // Insert some default blog categories
  await connection.query(`
    INSERT IGNORE INTO blogCategories (id, name, slug, description, color) VALUES
    ('cat-1', 'Technology', 'technology', 'Latest trends and insights in technology', '#3b82f6'),
    ('cat-2', 'Design', 'design', 'UI/UX design principles and best practices', '#8b5cf6'),
    ('cat-3', 'Business', 'business', 'Business strategies and entrepreneurship', '#10b981'),
    ('cat-4', 'Lifestyle', 'lifestyle', 'Life tips and personal development', '#f59e0b'),
    ('cat-5', 'Travel', 'travel', 'Travel guides and experiences', '#ef4444')
  `);

  console.log("Database setup complete!");

  await connection.end();
}

main().catch((err) => {
  console.error("Error setting up database:", err);
  process.exit(1);
});
