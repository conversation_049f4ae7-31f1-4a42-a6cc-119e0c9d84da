import mysql from 'mysql2/promise';
import * as dotenv from 'dotenv';

dotenv.config();

async function createBlogDislikesTable() {
  console.log('🔧 Creating blogDislikes table...');

  let connection: mysql.Connection | null = null;

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USERNAME,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('✅ Connected to database');

    // Check if table already exists
    const [existingTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'blogDislikes'
    `, [process.env.DATABASE_NAME]);

    if ((existingTables as any[]).length > 0) {
      console.log('⚠️  blogDislikes table already exists');
      return;
    }

    // Create blogDislikes table
    await connection.execute(`
      CREATE TABLE blogDislikes (
        id varchar(255) NOT NULL,
        blogId varchar(255) NOT NULL,
        userId varchar(255) NOT NULL,
        createdAt timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY blogDislikes_blogId_idx (blogId),
        KEY blogDislikes_userId_idx (userId),
        UNIQUE KEY blogDislikes_blogId_userId_unique (blogId, userId),
        CONSTRAINT blogDislikes_blogId_fkey FOREIGN KEY (blogId) REFERENCES blogs (id) ON DELETE CASCADE,
        CONSTRAINT blogDislikes_userId_fkey FOREIGN KEY (userId) REFERENCES users (id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log('✅ blogDislikes table created successfully');

    // Verify table creation
    const [newTables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'blogDislikes'
    `, [process.env.DATABASE_NAME]);

    if ((newTables as any[]).length > 0) {
      console.log('✅ Table verification successful');
    } else {
      console.log('❌ Table verification failed');
    }

  } catch (error) {
    console.error('❌ Error creating blogDislikes table:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

if (require.main === module) {
  createBlogDislikesTable()
    .then(() => {
      console.log('🎉 Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}
