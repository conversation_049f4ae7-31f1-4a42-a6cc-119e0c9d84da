"use client";

import { useState, useEffect, use<PERSON>allback, useMemo, useRef } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { BlogCard } from "@/components/blog/BlogCard";
import { AuthorBlogStats } from "@/components/blog/AuthorBlogStats";
import { AuthorBlogList } from "@/components/blog/AuthorBlogList";
import { TrendingBlogs } from "@/components/blog/TrendingBlogs";

import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Badge } from "@/components/ui/Badge";
import {
  PlusIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  SparklesIcon,
  ClockIcon,
  FireIcon,
  Bars3Icon,
  XMarkIcon,
  EyeIcon
} from "@heroicons/react/24/outline";
import Link from "next/link";

interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt?: string | null;
  coverImage?: string | null;
  readTime?: number | null;
  viewCount?: number;
  publishedAt?: string | null;
  featured?: boolean;
  author: {
    id: string;
    name: string;
    image?: string | null;
    username?: string | null;
  };
  category?: {
    id: string;
    name: string;
    color: string;
  } | null;
  tags?: string[] | null;
  _count?: {
    likes: number;
    comments: number;
  };
  isLiked?: boolean;
  isBookmarked?: boolean;
  monetization?: {
    id: string;
    isEnabled: boolean;
    isApproved: boolean;
    status: 'pending' | 'approved' | 'rejected' | 'suspended';
    cprRate: string;
    totalEarnings: string;
    rejectionReason?: string;
  } | null;
}

interface Category {
  id: string;
  name: string;
  slug: string;
  color: string;
  _count: {
    blogs: number;
  };
}

export default function BlogsPage() {
  const { data: session } = useSession();
  const router = useRouter();

  // Memoize user ID to prevent unnecessary re-renders
  const userId = useMemo(() => session?.user?.id, [session?.user?.id]);
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [featuredBlogs, setFeaturedBlogs] = useState<Blog[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("all");
  const [sortBy, setSortBy] = useState("latest");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [userBlogs, setUserBlogs] = useState<Blog[]>([]);
  const [userBlogsLoading, setUserBlogsLoading] = useState(false);
  const [userBlogsFilter, setUserBlogsFilter] = useState<'all' | 'published' | 'draft' | 'monetized'>('all');
  const userBlogsCacheRef = useRef<{ [key: string]: Blog[] }>({});

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  // Fetch blogs from API
  const fetchBlogs = useCallback(async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        status: "published",
      });

      if (selectedCategory) {
        params.append("category", selectedCategory);
      }

      if (searchQuery) {
        params.append("search", searchQuery);
      }

      // Add authorId for "Your News/Blog" tab
      if (activeTab === "your" && userId) {
        params.append("authorId", userId);
      }

      const response = await fetch(`/api/blogs?${params}`);
      if (response.ok) {
        const data = await response.json();
        setBlogs(data.blogs);
        setFeaturedBlogs(data.blogs.filter((blog: Blog) => blog.featured));
        setPagination(data.pagination);
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Failed to fetch blogs' }));
        setError(errorData.message || 'Failed to fetch blogs');
        console.error("Failed to fetch blogs");
      }
    } catch (error) {
      setError('Network error occurred while fetching blogs');
      console.error("Error fetching blogs:", error);
    } finally {
      setLoading(false);
    }
  }, [selectedCategory, searchQuery, pagination.page, activeTab, userId]);

  // Fetch categories from API
  const fetchCategories = useCallback(async () => {
    try {
      const response = await fetch("/api/blogs/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      } else {
        console.error("Failed to fetch categories");
        // Don't set error for categories as it's not critical
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      // Don't set error for categories as it's not critical
    }
  }, []);

  // Optimized user blogs fetching with caching
  const fetchUserBlogs = useCallback(async (filter: string = 'all') => {
    if (!userId) return;

    const cacheKey = `${userId}-${filter}`;

    // Check cache first
    if (userBlogsCacheRef.current[cacheKey]) {
      setUserBlogs(userBlogsCacheRef.current[cacheKey]);
      return;
    }

    try {
      setUserBlogsLoading(true);
      const params = new URLSearchParams({
        authorId: userId,
        status: filter === 'all' ? '' : filter === 'monetized' ? 'published' : filter,
        includeMonetization: 'true',
        limit: '20' // Load more items for better UX
      });

      const response = await fetch(`/api/blogs?${params}`);
      if (response.ok) {
        const data = await response.json();
        let filteredBlogs = data.blogs;

        if (filter === 'monetized') {
          filteredBlogs = data.blogs.filter((blog: Blog) => blog.monetization);
        }

        // Cache the results
        userBlogsCacheRef.current[cacheKey] = filteredBlogs;
        setUserBlogs(filteredBlogs);
      } else {
        console.error('Failed to fetch user blogs');
      }
    } catch (error) {
      console.error('Error fetching user blogs:', error);
    } finally {
      setUserBlogsLoading(false);
    }
  }, [userId]);

  // Clear user blogs cache when needed
  const clearUserBlogsCache = useCallback(() => {
    if (userId) {
      Object.keys(userBlogsCacheRef.current).forEach(key => {
        if (key.startsWith(userId)) {
          delete userBlogsCacheRef.current[key];
        }
      });
    }
  }, [userId]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchBlogs();
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [fetchBlogs]);

  // Reset to "all" tab if user logs out while on "your" tab
  useEffect(() => {
    if (activeTab === "your" && !userId) {
      setActiveTab("all");
    }
  }, [userId, activeTab]);

  // Prefetch user blogs when user is available (for faster tab switching)
  useEffect(() => {
    if (userId && activeTab !== 'your') {
      // Prefetch in background with a small delay
      const timeoutId = setTimeout(() => {
        fetchUserBlogs('all');
      }, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [userId, fetchUserBlogs, activeTab]);

  // Fetch user blogs when tab changes to "your"
  useEffect(() => {
    if (activeTab === 'your' && userId) {
      fetchUserBlogs(userBlogsFilter);
    }
  }, [activeTab, userId, userBlogsFilter, fetchUserBlogs]);

  // Clear error when data changes
  useEffect(() => {
    if (error) {
      setError(null);
    }
  }, [blogs, categories]);

  const handleLike = async (blogId: string) => {
    const blog = blogs.find(b => b.id === blogId);
    if (!blog || actionLoading === `like-${blogId}`) return;

    try {
      setActionLoading(`like-${blogId}`);
      const response = await fetch(`/api/blogs/${blog.slug}/like`, {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        setBlogs(prev => prev.map(b => {
          if (b.id === blogId) {
            return {
              ...b,
              isLiked: data.isLiked,
              _count: {
                ...b._count!,
                likes: data.isLiked ? b._count!.likes + 1 : b._count!.likes - 1
              }
            };
          }
          return b;
        }));

        // Also update user blogs if in "your" tab
        if (activeTab === 'your') {
          setUserBlogs(prev => prev.map(b => {
            if (b.id === blogId) {
              return {
                ...b,
                isLiked: data.isLiked,
                _count: {
                  ...b._count!,
                  likes: data.isLiked ? b._count!.likes + 1 : b._count!.likes - 1
                }
              };
            }
            return b;
          }));
        }
      } else {
        console.error("Failed to toggle like");
      }
    } catch (error) {
      console.error("Error toggling like:", error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleBookmark = async (blogId: string) => {
    const blog = blogs.find(b => b.id === blogId);
    if (!blog || actionLoading === `bookmark-${blogId}`) return;

    try {
      setActionLoading(`bookmark-${blogId}`);
      const response = await fetch(`/api/blogs/${blog.slug}/bookmark`, {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        setBlogs(prev => prev.map(b =>
          b.id === blogId ? { ...b, isBookmarked: data.isBookmarked } : b
        ));

        // Also update user blogs if in "your" tab
        if (activeTab === 'your') {
          setUserBlogs(prev => prev.map(b =>
            b.id === blogId ? { ...b, isBookmarked: data.isBookmarked } : b
          ));
        }
      } else {
        console.error("Failed to toggle bookmark");
      }
    } catch (error) {
      console.error("Error toggling bookmark:", error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleShare = (blogId: string) => {
    const blog = blogs.find(b => b.id === blogId);
    if (blog) {
      navigator.clipboard.writeText(`${window.location.origin}/blogs/${blog.slug}`);
      // You could show a toast notification here
    }
  };

  const handleEdit = (blogId: string, slug: string) => {
    router.push(`/blogs/${slug}/edit`);
  };

  const handleDelete = async (blogId: string, slug: string) => {
    if (!confirm('Are you sure you want to delete this blog post? This action cannot be undone.')) {
      return;
    }

    if (actionLoading === `delete-${blogId}`) return;

    try {
      setActionLoading(`delete-${blogId}`);
      const response = await fetch(`/api/blogs/${slug}`, {
        method: "DELETE",
      });

      if (response.ok) {
        // Remove the blog from the local state
        setBlogs(prev => prev.filter(b => b.id !== blogId));
        setFeaturedBlogs(prev => prev.filter(b => b.id !== blogId));

        // Also remove from user blogs and clear cache
        setUserBlogs(prev => prev.filter(b => b.id !== blogId));
        // Clear cache for all user blog filters
        Object.keys(userBlogsCacheRef.current).forEach(key => {
          if (key.startsWith(userId!)) {
            userBlogsCacheRef.current[key] = userBlogsCacheRef.current[key].filter(b => b.id !== blogId);
          }
        });
        // Show success message (you could replace with toast)
        console.log("Blog post deleted successfully!");
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Failed to delete blog' }));
        setError(`Failed to delete blog: ${errorData.message}`);
      }
    } catch (error) {
      console.error("Error deleting blog:", error);
      setError("An error occurred while deleting the blog post.");
    } finally {
      setActionLoading(null);
    }
  };

  const filteredBlogs = blogs.filter(blog => {
    const matchesSearch = blog.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         blog.excerpt?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || blog.category?.id === selectedCategory;
    const matchesTab = activeTab === "all" ||
                      (activeTab === "following" && blog.isBookmarked) ||
                      (activeTab === "your" && userId && blog.author.id === userId);

    return matchesSearch && matchesCategory && matchesTab;
  });

  const sortedBlogs = [...filteredBlogs].sort((a, b) => {
    switch (sortBy) {
      case "popular":
        return (b._count?.likes || 0) - (a._count?.likes || 0);
      case "views":
        return (b.viewCount || 0) - (a.viewCount || 0);
      case "oldest":
        return new Date(a.publishedAt || 0).getTime() - new Date(b.publishedAt || 0).getTime();
      default: // latest
        return new Date(b.publishedAt || 0).getTime() - new Date(a.publishedAt || 0).getTime();
    }
  });

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Error Display */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-xl p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setError(null)}
                  className="inline-flex text-red-400 hover:text-red-600 focus:outline-none"
                >
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}
        {/* Header */}
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl p-8 lg:p-12 mb-12">
          {/* Background decorations */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-indigo-200/30 to-pink-200/30 rounded-full translate-y-24 -translate-x-24"></div>

          <div className="relative z-10 flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="max-w-2xl">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 leading-tight">
                Discover Amazing
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  News/Blog
                </span>
              </h1>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                Explore insights, tutorials, and inspiring news/blog from our vibrant community of writers and creators.
              </p>

              {/* Stats */}
              <div className="flex items-center space-x-6 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <DocumentTextIcon className="w-5 h-5 text-blue-600" />
                  <span className="font-medium">{blogs.length} News/Blog</span>
                </div>
                <div className="flex items-center space-x-2">
                  <SparklesIcon className="w-5 h-5 text-purple-600" />
                  <span className="font-medium">{featuredBlogs.length} Featured</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FireIcon className="w-5 h-5 text-orange-600" />
                  <span className="font-medium">{categories.length} Categories</span>
                </div>
              </div>
            </div>

            <div className="mt-8 lg:mt-0 lg:ml-8">
              <Link href="/blogs/create">
                <Button
                  size="lg"
                  className="w-full lg:w-auto bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Write Your News/Blog
                </Button>
              </Link>

              {userId && (
                <p className="text-sm text-gray-500 mt-3 text-center lg:text-left">
                  Share your knowledge with the world
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-10 space-y-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Mobile Category Toggle + Search */}
            <div className="flex items-center space-x-3 flex-1">
              {/* Mobile Category Toggle */}
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="lg:hidden flex items-center justify-center w-10 h-10 bg-white border border-gray-200 rounded-xl shadow-sm hover:bg-gray-50 transition-colors"
              >
                {sidebarOpen ? (
                  <XMarkIcon className="h-5 w-5 text-gray-600" />
                ) : (
                  <Bars3Icon className="h-5 w-5 text-gray-600" />
                )}
              </button>

              {/* Search */}
              <div className="relative flex-1 max-w-lg">
                <MagnifyingGlassIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="search"
                  placeholder="Search news/blog, authors, topics..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 pr-4 py-3 w-full rounded-xl border-gray-200 focus:border-blue-500 focus:ring-blue-500 shadow-sm text-base"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
            </div>

            {/* Sort */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <FunnelIcon className="h-5 w-5 text-gray-400" />
                <span className="font-medium">Sort by:</span>
              </div>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border border-gray-200 rounded-xl px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white shadow-sm font-medium"
              >
                <option value="latest">Latest First</option>
                <option value="popular">Most Popular</option>
                <option value="views">Most Viewed</option>
                <option value="oldest">Oldest First</option>
              </select>
            </div>
          </div>

          {/* Tabs */}
          <div className="flex flex-wrap gap-2 bg-white p-2 rounded-2xl shadow-sm border border-gray-100">
            {[
              { id: "all", label: "All News/Blog", icon: DocumentTextIcon, count: blogs.length },
              { id: "following", label: "Following", icon: SparklesIcon, count: 0 },
              ...(userId ? [{ id: "your", label: "Your News/Blog", icon: ClockIcon, count: userBlogs.length || blogs.filter(b => b.author.id === userId).length }] : []),
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-200 ${
                  activeTab === tab.id
                    ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg transform scale-105"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
                {tab.count > 0 && (
                  <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                    activeTab === tab.id
                      ? "bg-white/20 text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Main Content with Sidebar Layout */}
        <div className="relative flex flex-col lg:flex-row gap-8">
          {/* Mobile Sidebar Overlay */}
          {sidebarOpen && (
            <div
              className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* Left Sidebar - Categories */}
          <div className={`lg:w-80 flex-shrink-0 ${
            sidebarOpen
              ? "fixed top-0 left-0 h-full w-80 z-50 lg:relative lg:z-auto transform translate-x-0"
              : "fixed top-0 left-0 h-full w-80 z-50 lg:relative lg:z-auto transform -translate-x-full lg:translate-x-0"
          } transition-transform duration-300 ease-in-out lg:transition-none`}>
            <div className="bg-white rounded-none lg:rounded-2xl shadow-lg lg:shadow-sm border-r lg:border border-gray-100 p-6 h-full lg:h-auto lg:sticky lg:top-8 overflow-y-auto">
              {/* Mobile Close Button */}
              <div className="lg:hidden flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">Categories</h2>
                <button
                  onClick={() => setSidebarOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <XMarkIcon className="h-5 w-5 text-gray-600" />
                </button>
              </div>

              <div className="mb-6 hidden lg:block">
                <h2 className="text-xl font-bold text-gray-900 flex items-center mb-2">
                  <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  Categories
                </h2>
                <p className="text-sm text-gray-500">
                  {categories.length} categories available
                </p>
              </div>

              <div className="space-y-2">
                <button
                  onClick={() => {
                    setSelectedCategory(null);
                    setSidebarOpen(false);
                  }}
                  className={`w-full text-left px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                    !selectedCategory
                      ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md"
                      : "text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                      <span>All Categories</span>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                      !selectedCategory ? "bg-white/20 text-white" : "bg-gray-100 text-gray-600"
                    }`}>
                      {blogs.length}
                    </span>
                  </div>
                </button>

                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => {
                      setSelectedCategory(category.id);
                      setSidebarOpen(false);
                    }}
                    className={`w-full text-left px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 ${
                      selectedCategory === category.id
                        ? "text-white shadow-md"
                        : "text-gray-700 hover:bg-gray-50"
                    }`}
                    style={{
                      backgroundColor: selectedCategory === category.id ? category.color : undefined
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`w-3 h-3 rounded-full ${
                            selectedCategory === category.id ? "bg-white/30" : ""
                          }`}
                          style={{
                            backgroundColor: selectedCategory !== category.id ? category.color : undefined
                          }}
                        ></div>
                        <span>{category.name}</span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                        selectedCategory === category.id
                          ? "bg-white/20 text-white"
                          : "bg-gray-100 text-gray-600"
                      }`}>
                        {category._count.blogs}
                      </span>
                    </div>
                  </button>
                ))}
              </div>

              {/* Quick Stats */}
              <div className="mt-6 pt-6 border-t border-gray-100">
                <h3 className="text-sm font-semibold text-gray-900 mb-3">Quick Stats</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Total Stories</span>
                    <span className="font-medium">{blogs.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Featured</span>
                    <span className="font-medium">{featuredBlogs.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Categories</span>
                    <span className="font-medium">{categories.length}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Content Area */}
          <div className="flex-1 min-w-0">

            {/* Featured Blogs - Hidden on Your News/Blog tab */}
            {featuredBlogs.length > 0 && activeTab !== "your" && (
              <div className="mb-16">
                <div className="flex items-center justify-between mb-8">
                  <div>
                    <h2 className="text-3xl font-bold text-gray-900 flex items-center mb-2">
                      <div className="bg-gradient-to-r from-orange-400 to-red-500 p-2 rounded-xl mr-3">
                        <FireIcon className="h-6 w-6 text-white" />
                      </div>
                      Featured News/Blog
                    </h2>
                    <p className="text-gray-600">Hand-picked news/blog from our community</p>
                  </div>

                  <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
                    <span className="font-medium">{featuredBlogs.length} featured</span>
                    <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {featuredBlogs.map((blog, index) => (
                    <div
                      key={blog.id}
                      className="transform transition-all duration-500"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <BlogCard
                        blog={blog}
                        variant="featured"
                        currentUserId={userId}
                        onLike={handleLike}
                        onBookmark={handleBookmark}
                        onShare={handleShare}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Trending Blogs Section */}
            {activeTab !== "your" && (
              <div className="mb-12">
                <TrendingBlogs limit={5} showHeader={true} />
              </div>
            )}

            {/* All Stories */}
            <div className="mb-12">
              <div className="flex items-center justify-between mb-8">
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-2">
                    {activeTab === "all" && "All News/Blog"}
                    {activeTab === "following" && "Following"}
                    {activeTab === "your" && "Your News/Blog"}
                    {selectedCategory && (
                      <span className="text-blue-600">
                        {" "}• {categories.find(c => c.id === selectedCategory)?.name}
                      </span>
                    )}
                  </h2>
                  <p className="text-gray-600">
                    {activeTab === "all" && "Discover amazing news/blog from our community"}
                    {activeTab === "following" && "News/Blog from people you follow"}
                    {activeTab === "your" && "Your published news/blog"}
                  </p>
                </div>

                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900">
                    {sortedBlogs.length}
                  </p>
                  <p className="text-sm text-gray-500">
                    {sortedBlogs.length === 1 ? 'news/blog' : 'news/blogs'}
                  </p>
                </div>
              </div>

              {/* Author Stats for "Your News/Blog" tab */}
              {activeTab === "your" && userId && (
                <div className="mb-8">
                  <AuthorBlogStats authorId={userId} />
                </div>
              )}

              {sortedBlogs.length === 0 ? (
                <div className="text-center py-16">
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl p-12 max-w-md mx-auto">
                    <div className="bg-white rounded-2xl p-6 shadow-sm mb-6 inline-block">
                      <DocumentTextIcon className="w-12 h-12 text-gray-400 mx-auto" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">No news/blog found</h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {searchQuery
                        ? 'Try adjusting your search terms or browse different categories.'
                        : activeTab === 'your'
                          ? 'You haven\'t written any news/blog yet. Start sharing your thoughts!'
                          : 'Be the first to share an amazing news/blog with our community!'}
                    </p>
                    {!searchQuery && (
                      <div className="space-y-3">
                        <Link href="/blogs/create">
                          <Button
                            size="lg"
                            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
                          >
                            <PlusIcon className="h-5 w-5 mr-2" />
                            Write Your First News/Blog
                          </Button>
                        </Link>
                        <p className="text-sm text-gray-500">
                          Share your knowledge and inspire others
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ) : activeTab === "your" && userId ? (
                <div className="space-y-6">
                  {/* Enhanced Filter Tabs */}
                  <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                      <div className="flex items-center gap-3">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">Your Content</h3>
                          <p className="text-sm text-gray-500 mt-1">Manage and track your blog posts</p>
                        </div>
                        <button
                          onClick={() => {
                            clearUserBlogsCache();
                            fetchUserBlogs(userBlogsFilter);
                          }}
                          className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                          title="Refresh"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                        </button>
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {[
                          { id: 'all', label: 'All Posts', count: userBlogs.length },
                          { id: 'published', label: 'Published', count: userBlogs.filter(b => b.status === 'published').length },
                          { id: 'draft', label: 'Drafts', count: userBlogs.filter(b => b.status === 'draft').length },
                          { id: 'monetized', label: 'Monetized', count: userBlogs.filter(b => b.monetization).length }
                        ].map((filter) => (
                          <button
                            key={filter.id}
                            onClick={() => {
                              setUserBlogsFilter(filter.id as any);
                              fetchUserBlogs(filter.id);
                            }}
                            className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 ${
                              userBlogsFilter === filter.id
                                ? 'bg-blue-600 text-white shadow-md'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                          >
                            {filter.label}
                            {filter.count > 0 && (
                              <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                                userBlogsFilter === filter.id
                                  ? 'bg-white/20 text-white'
                                  : 'bg-white text-gray-600'
                              }`}>
                                {filter.count}
                              </span>
                            )}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-blue-900">Total Posts</p>
                          <p className="text-2xl font-bold text-blue-600">{userBlogs.length}</p>
                        </div>
                        <DocumentTextIcon className="h-8 w-8 text-blue-500" />
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-green-900">Published</p>
                          <p className="text-2xl font-bold text-green-600">
                            {userBlogs.filter(b => b.status === 'published').length}
                          </p>
                        </div>
                        <EyeIcon className="h-8 w-8 text-green-500" />
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-purple-900">Total Views</p>
                          <p className="text-2xl font-bold text-purple-600">
                            {userBlogs.reduce((sum, blog) => sum + (blog.viewCount || 0), 0).toLocaleString()}
                          </p>
                        </div>
                        <SparklesIcon className="h-8 w-8 text-purple-500" />
                      </div>
                    </div>
                  </div>

                  {/* Blog List */}
                  {userBlogsLoading ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {[...Array(4)].map((_, index) => (
                        <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 animate-pulse">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                          <div className="space-y-2">
                            <div className="h-3 bg-gray-200 rounded"></div>
                            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : userBlogs.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="bg-gray-50 rounded-2xl p-8 max-w-md mx-auto">
                        <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No posts found</h3>
                        <p className="text-gray-600 mb-6">
                          {userBlogsFilter === 'all'
                            ? "You haven't written any blog posts yet."
                            : `No ${userBlogsFilter} posts found.`}
                        </p>
                        <Link href="/blogs/create">
                          <Button className="bg-blue-600 hover:bg-blue-700">
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Write Your First Post
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {userBlogs.map((blog, index) => (
                        <div
                          key={blog.id}
                          className="transform transition-all duration-300 opacity-0 animate-fadeInUp"
                          style={{
                            animationDelay: `${index * 50}ms`,
                            animationFillMode: 'forwards'
                          }}
                        >
                          <BlogCard
                            blog={blog}
                            variant="default"
                            currentUserId={userId}
                            onLike={handleLike}
                            onBookmark={handleBookmark}
                            onShare={handleShare}
                            onEdit={handleEdit}
                            onDelete={handleDelete}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {sortedBlogs.map((blog, index) => (
                    <div
                      key={blog.id}
                      className="transform transition-all duration-300 opacity-0 animate-fadeInUp"
                      style={{
                        animationDelay: `${index * 50}ms`,
                        animationFillMode: 'forwards'
                      }}
                    >
                      <BlogCard
                        blog={blog}
                        variant="default"
                        currentUserId={userId}
                        onLike={handleLike}
                        onBookmark={handleBookmark}
                        onShare={handleShare}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* Pagination */}
              {sortedBlogs.length > 0 && pagination.totalPages > 1 && (
                <div className="mt-12 flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-xl">
                  <div className="flex flex-1 justify-between sm:hidden">
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                      disabled={pagination.page === 1}
                      className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
                      disabled={pagination.page === pagination.totalPages}
                      className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                  <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing{" "}
                        <span className="font-medium">
                          {(pagination.page - 1) * pagination.limit + 1}
                        </span>{" "}
                        to{" "}
                        <span className="font-medium">
                          {Math.min(pagination.page * pagination.limit, pagination.total)}
                        </span>{" "}
                        of <span className="font-medium">{pagination.total}</span> results
                      </p>
                    </div>
                    <div>
                      <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm">
                        <button
                          onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                          disabled={pagination.page === 1}
                          className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <span className="sr-only">Previous</span>
                          <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" />
                          </svg>
                        </button>
                        {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                          const page = i + 1;
                          return (
                            <button
                              key={page}
                              onClick={() => setPagination(prev => ({ ...prev, page }))}
                              className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                                pagination.page === page
                                  ? 'z-10 bg-blue-600 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                                  : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                              }`}
                            >
                              {page}
                            </button>
                          );
                        })}
                        <button
                          onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
                          disabled={pagination.page === pagination.totalPages}
                          className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <span className="sr-only">Next</span>
                          <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Trending Topics */}
        <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-3xl p-8 lg:p-12 mb-12">
          {/* Background decorations */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-purple-200/20 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-indigo-200/20 to-pink-200/20 rounded-full translate-y-12 -translate-x-12"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-xl mr-3">
                    <SparklesIcon className="h-6 w-6 text-white" />
                  </div>
                  Trending Topics
                </h2>
                <p className="text-gray-600">Discover what's popular in our community</p>
              </div>

              <div className="hidden sm:block text-sm text-gray-500">
                <span className="font-medium">7 trending topics</span>
              </div>
            </div>

            <div className="flex flex-wrap gap-4">
              {['web-development', 'design', 'saas', 'remote-work', 'minimalism', 'travel', 'digital-nomad'].map((tag, index) => (
                <button
                  key={tag}
                  className="group px-6 py-3 bg-white/80 backdrop-blur-md text-gray-700 rounded-2xl text-sm font-semibold hover:bg-white hover:shadow-lg transition-all duration-300 transform hover:scale-105 border border-white/50"
                  onClick={() => setSearchQuery(tag)}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <span className="flex items-center space-x-2">
                    <span className="text-blue-600 group-hover:text-purple-600 transition-colors">#</span>
                    <span>{tag.replace('-', ' ')}</span>
                    <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-60 group-hover:opacity-100 transition-opacity"></div>
                  </span>
                </button>
              ))}
            </div>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                Click on any topic to explore related stories
              </p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
