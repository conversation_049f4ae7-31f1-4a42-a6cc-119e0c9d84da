import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { db } from "@/lib/db";
import { groups, groupMembers, users, posts } from "@/lib/db/schema";
import { eq, and, desc, count } from "drizzle-orm";

// Import components
import { GroupHeader } from "@/components/groups/GroupHeader";
import { GroupTabs } from "@/components/groups/GroupTabs";
import { GroupSidebar } from "@/components/groups/GroupSidebar";
import { GroupPostFormPlaceholder } from "@/components/groups/GroupPostFormPlaceholder";
import { GroupPostsList } from "@/components/groups/GroupPostsList";
import { GroupNotFound } from "@/components/groups/GroupNotFound";

interface GroupPageProps {
  params: Promise<{
    groupId: string;
  }>;
}

export default async function GroupPage({ params }: GroupPageProps) {
  const user = await requireAuth();
  const resolvedParams = await params;
  const { groupId } = resolvedParams;

  // Fetch the group
  const group = await db.select({
    id: groups.id,
    name: groups.name,
    description: groups.description,
    visibility: groups.visibility,
    coverImage: groups.coverImage,
    category: groups.category,
    rules: groups.rules,
    creatorId: groups.creatorId,
    createdAt: groups.createdAt,
    updatedAt: groups.updatedAt
  })
  .from(groups)
  .where(eq(groups.id, groupId))
  .limit(1)
  .then(results => results[0]);

  // Fetch creator info
  const creator = group ? await db.query.users.findFirst({
    where: eq(users.id, group.creatorId),
    columns: {
      id: true,
      name: true,
      username: true,
      image: true,
    },
  }) : null;

  if (!group) {
    // If group doesn't exist, show a nice error message instead of 404
    return (
      <MainLayout>
        <GroupNotFound />
      </MainLayout>
    );
  }

  // Get member count
  const membersCount = await db
    .select({ count: count() })
    .from(groupMembers)
    .where(eq(groupMembers.groupId, groupId));

  // Get user's role in the group
  const userMembership = await db.query.groupMembers.findFirst({
    where: and(
      eq(groupMembers.groupId, groupId),
      eq(groupMembers.userId, user.id)
    ),
  });

  // Get post count
  const postsCount = await db
    .select({ count: count() })
    .from(posts)
    .where(eq(posts.groupId, groupId));

  // Check if user has access to the group
  const isPublic = group.visibility === "public";
  const isPrivateVisible = group.visibility === "private-visible";
  const isMember = Boolean(userMembership && (userMembership.role === "admin" || userMembership.role === "moderator" || userMembership.role === "member"));
  const isAdmin = Boolean(userMembership && userMembership.role === "admin");
  const isModerator = Boolean(userMembership && userMembership.role === "moderator");
  const isPending = Boolean(userMembership && userMembership.role === "pending");
  const isCreator = group && group.creatorId === user.id;

  // Fetch recent members
  const recentMembers = await db.select({
    member: groupMembers,
    user: users,
  })
  .from(groupMembers)
  .innerJoin(users, eq(groupMembers.userId, users.id))
  .where(eq(groupMembers.groupId, groupId))
  .orderBy(desc(groupMembers.createdAt))
  .limit(6);

  // Fetch recent posts if user has access
  const canViewPosts = isPublic || isMember || isCreator;

  const rawPosts = canViewPosts
    ? await db.query.posts.findMany({
        where: eq(posts.groupId, groupId),
        orderBy: [desc(posts.createdAt)],
        limit: 5,
        with: {
          user: {
            columns: {
              id: true,
              name: true,
              username: true,
              image: true,
            },
          },
          likes: true,
          comments: {
            columns: {
              id: true,
            },
          },
        },
      })
    : [];

  // Transform posts to match expected interface
  const recentPosts = rawPosts.map(post => ({
    ...post,
    type: 'group_post' as const,
    group: {
      id: group.id,
      name: group.name,
      slug: group.id, // Using id as slug for now
      profileImage: group.coverImage,
      isPrivate: group.visibility !== 'public'
    },
    _count: {
      likes: post.likes?.length || 0,
      dislikes: 0, // Not implemented yet
      comments: post.comments?.length || 0,
      shares: 0 // Not implemented yet
    },
    liked: false, // Will be determined by user interaction
    disliked: false // Will be determined by user interaction
  }));

  return (
    <MainLayout>
      {/* Hero section with cover image */}
      <GroupHeader
        group={group}
        membersCount={membersCount[0]?.count || 0}
        isCreator={isCreator}
        isAdmin={isAdmin}
        isMember={isMember}
        isPending={isPending}
        isPublic={isPublic}
        isPrivateVisible={isPrivateVisible}
      />

      {/* Tabs navigation */}
      <GroupTabs groupId={groupId} activeTab="posts" />

      {/* Main content */}
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Left sidebar */}
          <GroupSidebar
            group={group}
            creator={creator || null}
            recentMembers={recentMembers}
            membersCount={membersCount[0]?.count || 0}
          />

          {/* Main content area */}
          <div className="lg:col-span-2 space-y-6">
            {/* Create post box */}
            {isMember && (
              <GroupPostFormPlaceholder
                groupId={groupId}
                groupName={group.name}
                userImage={user.image || null}
                userName={user.name || null}
                isAdmin={isAdmin}
                isModerator={isModerator}
              />
            )}

            {/* Posts */}
            <GroupPostsList
              groupId={groupId}
              groupName={group.name}
              posts={recentPosts}
              canViewPosts={canViewPosts}
              isMember={isMember}
              isPublic={isPublic}
            />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
