"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import Image from "next/image";
import { Button } from "@/components/ui/Button";
import { LeftSidebar } from "@/components/layout/LeftSidebar";
import Link from "next/link";
import { UserCircleIcon, MapPinIcon, CalendarIcon, PencilIcon, CameraIcon, ChatBubbleLeftRightIcon, EllipsisHorizontalIcon, UserPlusIcon, UserMinusIcon } from "@heroicons/react/24/outline";
import { ProfilePhotoUploader } from "@/components/profile/ProfilePhotoUploader";
import { CoverPhotoUploader } from "@/components/profile/CoverPhotoUploader";
import { ProfileEditor } from "@/components/profile/ProfileEditor";
import { ProfilePosts } from "@/components/profile/ProfilePosts";
import { ProfileTabs } from "@/components/profile/ProfileTabs";
import { ProfileAbout } from "@/components/profile/ProfileAbout";
import { ProfilePhotos } from "@/components/profile/ProfilePhotos";
import { ProfileConnections } from "@/components/profile/ProfileConnections";
import { ProfileActivity } from "@/components/profile/ProfileActivity";
import { ProfileLikedPosts } from "@/components/profile/ProfileLikedPosts";
import { ProfileBlogs } from "@/components/profile/ProfileBlogs";
import { ProfileProducts } from "@/components/profile/ProfileProducts";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";

import { subscribeToUser, unsubscribeFromUser, getSubscriptionStatus } from "@/app/actions/subscription";



interface SubscriptionStatus {
  isSubscribed: boolean;
  isSubscribedBack: boolean;
  subscriptionId: string | null;
}

interface UserData {
  id: string;
  name: string;
  username: string;
  email?: string;
  image: string | null;
  coverImage: string | null;
  bio: string | null;
  location: string | null;
  birthday: string | null;
  createdAt: string;
  website?: string | null;
  facebook?: string | null;
  twitter?: string | null;
  instagram?: string | null;
  linkedin?: string | null;
  youtube?: string | null;
  work?: string | null;
  education?: string | null;

  // Statistics
  _count?: {
    posts: number;
    subscribers: number;
    photos: number;
    blogs: number;
    products: number;
  };

  subscriptionStatus?: SubscriptionStatus | null;
}

export default function UserProfilePage() {
  const { data: session } = useSession();
  const params = useParams();
  const username = params?.username as string;
  const router = useRouter();

  const [showProfileUploader, setShowProfileUploader] = useState(false);
  const [showCoverUploader, setShowCoverUploader] = useState(false);
  const [showProfileEditor, setShowProfileEditor] = useState(false);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('posts');

  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [subscribedCount, setSubscribedCount] = useState<number>(0);

  // Fetch function for profile data
  const fetchUserProfileData = useCallback(async () => {
    const response = await fetch(`/api/users/username/${username}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch user profile: ${response.status}`);
    }
    return await response.json();
  }, [username]);

  useEffect(() => {
    async function fetchUserProfile() {
      if (!session?.user) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Fetch user data directly to avoid caching issues
        const response = await fetch(`/api/users/username/${username}`);
        if (!response.ok) {
          if (response.status === 404) {
            setError("User not found");
            return;
          }
          throw new Error(`Failed to fetch user profile: ${response.status}`);
        }

        const data = await response.json();
        console.log('Fetched user data by username:', data);
        setUserData(data);

        // Fetch subscription status if not viewing own profile
        if (data.id !== session.user.id) {
          try {
            const subscriptionResult = await getSubscriptionStatus(data.id);
            if (subscriptionResult.success) {
              setSubscriptionStatus({
                isSubscribed: subscriptionResult.isSubscribed || false,
                isSubscribedBack: subscriptionResult.isSubscribedBack || false,
                subscriptionId: subscriptionResult.subscriptionId || null,
              });
            }
          } catch (error) {
            console.error('Error fetching subscription status:', error);
          }
        }

        // Fetch subscribed count (how many users this user is subscribed to)
        try {
          const subscribedResponse = await fetch(`/api/users/${data.id}/subscribed-count`);
          if (subscribedResponse.ok) {
            const subscribedData = await subscribedResponse.json();
            setSubscribedCount(subscribedData.count || 0);
          }
        } catch (error) {
          console.error('Error fetching subscribed count:', error);
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
        if (error instanceof Error && error.message.includes('404')) {
          setError("User not found");
        } else {
          setError("An error occurred while loading the profile");
        }
      } finally {
        setIsLoading(false);
      }
    }

    fetchUserProfile();
  }, [session, username]);

  // Refresh handler for after uploads
  const handleProfileUpdate = async () => {
    if (!session?.user || !userData) return;

    try {
      // Force fetch fresh data (bypasses cache)
      const data = await fetchUserProfileData();
      console.log('Updated user data after upload:', data);
      setUserData(data);
    } catch (error) {
      console.error('Error refreshing user profile:', error);
    }
  };



  // Handler for navigating to messages with this user
  const handleMessage = () => {
    if (!session?.user || !userData) return;

    // Navigate to messages page with this user's ID
    router.push(`/messages?userId=${userData.id}`);
  };



  // Handler for subscribing to a user
  const handleSubscribe = async () => {
    if (!session?.user || !userData) return;

    try {
      const result = await subscribeToUser(userData.id);

      if (result.success) {
        console.log('Successfully subscribed');
        // Update the subscription status locally
        setSubscriptionStatus({
          isSubscribed: true,
          isSubscribedBack: subscriptionStatus?.isSubscribedBack || false,
          subscriptionId: result.subscriptionId || null,
        });
      } else {
        console.error('Failed to subscribe:', result.message);
      }
    } catch (error) {
      console.error('Error subscribing:', error);
    }
  };

  // Handler for unsubscribing from a user
  const handleUnsubscribe = async () => {
    if (!session?.user || !userData) return;

    try {
      const result = await unsubscribeFromUser(userData.id);

      if (result.success) {
        console.log('Successfully unsubscribed');
        // Update the subscription status locally
        setSubscriptionStatus({
          isSubscribed: false,
          isSubscribedBack: subscriptionStatus?.isSubscribedBack || false,
          subscriptionId: null,
        });
      } else {
        console.error('Failed to unsubscribe:', result.message);
      }
    } catch (error) {
      console.error('Error unsubscribing:', error);
    }
  };

  // Handler for reporting a profile
  const handleReportProfile = async () => {
    if (!session?.user || !userData) return;

    // Simple prompt for report reason
    const reason = prompt('Please select a reason for reporting this user:\n1. Spam\n2. Harassment\n3. Inappropriate content\n4. Impersonation\n5. Other');

    if (!reason) return;

    // Map the numeric input to the actual reason
    const reasonMap: Record<string, string> = {
      '1': 'spam',
      '2': 'harassment',
      '3': 'inappropriate_content',
      '4': 'impersonation',
      '5': 'other'
    };

    const selectedReason = reasonMap[reason] || 'other';

    // Get additional description if reason is 'other'
    let description = '';
    if (selectedReason === 'other') {
      description = prompt('Please provide additional details:') || '';
    }

    try {
      const response = await fetch(`/api/users/${userData.id}/report`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reason: selectedReason,
          description
        })
      });

      if (response.ok) {
        console.log('User reported successfully');
        // You could add a toast notification here
      } else {
        const data = await response.json();
        console.error('Failed to report user:', data.message);
      }
    } catch (error) {
      console.error('Error reporting user:', error);
    }
  };

  if (!session?.user) {
    return null;
  }

  // Show loading state while fetching data
  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-[calc(100vh-5rem)]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading profile...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-[calc(100vh-5rem)]">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">{error}</h2>
            <p className="text-gray-600 mb-4">The user you&apos;re looking for might not exist or you don&apos;t have permission to view this profile.</p>
            <Button onClick={() => router.push('/')}>
              Return to Home
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!userData) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center h-[calc(100vh-5rem)]">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Profile Not Found</h2>
            <p className="text-gray-600 mb-4">The user you&apos;re looking for might not exist.</p>
            <Button onClick={() => router.push('/')}>
              Return to Home
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  const isOwnProfile = session.user.id === userData.id;
  console.log('Rendering profile with user data:', userData);


  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row">
          {/* Left sidebar - 20% */}
          <div className="w-full lg:w-[20%] mb-5 lg:mb-0">
            <LeftSidebar />
            {/* This is an empty div that takes up the same space as the fixed sidebar */}
            <div className="hidden lg:block h-[calc(100vh-5rem)]"></div>
          </div>

          {/* Gap between left sidebar and main content - 5% */}
          <div className="hidden lg:block lg:w-[5%]"></div>

          {/* Main content - 75% */}
          <div className="w-full lg:w-[75%] space-y-6">
            {/* Profile header */}
            <div className="overflow-hidden rounded-xl bg-white shadow-sm hover:shadow transition-all duration-300">
              <div className="relative h-56 bg-gradient-to-r from-blue-500 to-indigo-600">
                {/* Cover image */}
                {userData.coverImage ? (
                  <Image
                    src={userData.coverImage}
                    alt="Cover"
                    fill
                    className="object-cover"
                  />
                ) : null}

                <div className="absolute -bottom-14 left-8">
                  <div className="relative h-28 w-28 overflow-hidden rounded-full border-4 border-white bg-gray-200 shadow-md transition-transform duration-300 hover:scale-105 group">
                    {userData.image ? (
                      <Image
                        src={userData.image}
                        alt={userData.name || "Profile"}
                        width={112}
                        height={112}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="h-full w-full flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-2xl font-bold">
                        {userData.name?.charAt(0).toUpperCase() || "U"}
                      </div>
                    )}

                    {/* Profile photo edit button - only show for own profile */}
                    {isOwnProfile && (
                      <button
                        onClick={() => setShowProfileUploader(true)}
                        className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      >
                        <CameraIcon className="h-8 w-8 text-white" />
                      </button>
                    )}
                  </div>
                </div>

                {/* Cover photo edit button - only show for own profile */}
                {isOwnProfile && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30"
                    onClick={() => setShowCoverUploader(true)}
                  >
                    <PencilIcon className="h-4 w-4 mr-1" />
                    Edit Cover
                  </Button>
                )}
              </div>
              <div className="px-8 pb-6 pt-16">
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">
                      {userData.name || "User"}
                    </h1>
                    <p className="text-sm text-gray-500">
                      @{userData.username}
                    </p>

                    {/* Subscriber & Subscribed Counter */}
                    <div className="flex items-center gap-4 mt-2">
                      <button
                        onClick={() => setActiveTab('connections')}
                        className="flex items-center gap-1 text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200 cursor-pointer"
                      >
                        <span className="font-semibold text-gray-900">{userData._count?.subscribers || 0}</span>
                        <span>Subscribers</span>
                      </button>
                      <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                      <button
                        onClick={() => setActiveTab('connections')}
                        className="flex items-center gap-1 text-sm text-gray-600 hover:text-blue-600 transition-colors duration-200 cursor-pointer"
                      >
                        <span className="font-semibold text-gray-900">{subscribedCount}</span>
                        <span>Subscribed</span>
                      </button>
                    </div>

                    {isOwnProfile && (
                      <p className="text-sm text-gray-500 mt-1">
                        {userData.email}
                      </p>
                    )}
                  </div>
                  {isOwnProfile ? (
                    <Button
                      variant="outline"
                      className="transition-all duration-300 hover:bg-blue-50"
                      onClick={() => setShowProfileEditor(true)}
                    >
                      <PencilIcon className="h-4 w-4 mr-1" />
                      Edit Profile
                    </Button>
                  ) : (
                    <div className="flex items-center space-x-2">
                      {/* Subscription Button */}
                      {subscriptionStatus?.isSubscribed ? (
                        <Button
                          variant="outline"
                          className="transition-all duration-300 bg-blue-50 text-blue-600 hover:bg-blue-100"
                          onClick={handleUnsubscribe}
                        >
                          <UserMinusIcon className="h-4 w-4 mr-1" />
                          Subscribed
                        </Button>
                      ) : (
                        <Button
                          variant="primary"
                          className="transition-all duration-300"
                          onClick={handleSubscribe}
                        >
                          <UserPlusIcon className="h-4 w-4 mr-1" />
                          Subscribe
                        </Button>
                      )}

                      <Button
                        variant="outline"
                        className="transition-all duration-300"
                        onClick={handleMessage}
                      >
                        <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
                        Message
                      </Button>

                      <Menu as="div" className="relative">
                        <MenuButton className="flex items-center justify-center h-9 w-9 rounded-full hover:bg-gray-100 transition-colors duration-200">
                          <EllipsisHorizontalIcon className="h-5 w-5 text-gray-500" />
                        </MenuButton>
                        <MenuItems anchor="bottom end" className="absolute z-10 mt-1 w-40 origin-top-right rounded-xl bg-white py-1 shadow-lg ring-1 ring-black/5 focus:outline-none">
                          <MenuItem>
                            <button
                              onClick={handleReportProfile}
                              className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                            >
                              Report Profile
                            </button>
                          </MenuItem>
                        </MenuItems>
                      </Menu>
                    </div>
                  )}
                </div>

              </div>
            </div>

            {/* Profile tabs */}
            <ProfileTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              isOwnProfile={isOwnProfile}
              postCount={userData._count?.posts || 0}
              photoCount={userData._count?.photos || 0}
              blogCount={userData._count?.blogs || 0}
              productCount={userData._count?.products || 0}
            />

            {/* Profile content */}
            <div className="space-y-6">
              {activeTab === 'posts' && (
                <div className="grid grid-cols-1 gap-8 md:grid-cols-5">
                  {/* Left column - wider */}
                  <div className="md:col-span-2 space-y-6">
                    {/* About section */}
                    <div className="overflow-hidden rounded-xl bg-white shadow-sm hover:shadow transition-all duration-300">
                      <div className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <h2 className="text-lg font-semibold text-gray-900">
                            About
                          </h2>
                          {isOwnProfile && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-500 hover:text-blue-600"
                              onClick={() => setShowProfileEditor(true)}
                            >
                              <PencilIcon className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                        <div className="space-y-4">
                          <div className="flex items-start">
                            <UserCircleIcon className="h-5 w-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
                            <div>
                              <p className="text-sm font-medium text-gray-500">
                                Bio
                              </p>
                              <p className="mt-1 text-sm text-gray-900">
                                {userData.bio || "No bio yet."}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start">
                            <MapPinIcon className="h-5 w-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
                            <div>
                              <p className="text-sm font-medium text-gray-500">
                                Location
                              </p>
                              <p className="mt-1 text-sm text-gray-900">
                                {userData.location || "Not specified"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start">
                            <CalendarIcon className="h-5 w-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
                            <div>
                              <p className="text-sm font-medium text-gray-500">
                                Joined
                              </p>
                              <p className="mt-1 text-sm text-gray-900">
                                {userData.createdAt ? new Date(userData.createdAt).toLocaleDateString() : "Unknown"}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Photos section */}
                    <div className="overflow-hidden rounded-xl bg-white shadow-sm hover:shadow transition-all duration-300">
                      <div className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <h2 className="text-lg font-semibold text-gray-900">
                            Photos
                          </h2>
                          <button
                            onClick={() => setActiveTab('photos')}
                            className="text-sm text-blue-600 hover:underline"
                          >
                            See all
                          </button>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          <div className="aspect-square bg-gray-100 rounded-md"></div>
                          <div className="aspect-square bg-gray-100 rounded-md"></div>
                          <div className="aspect-square bg-gray-100 rounded-md"></div>
                          <div className="aspect-square bg-gray-100 rounded-md"></div>
                          <div className="aspect-square bg-gray-100 rounded-md"></div>
                          <div className="aspect-square bg-gray-100 rounded-md"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Main content - posts - narrower */}
                  <div className="md:col-span-3 space-y-6">


                    {/* Posts section */}
                    <div>
                      <ProfilePosts userId={userData.id} isOwnProfile={isOwnProfile} />
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'about' && (
                <ProfileAbout
                  userData={userData}
                  isOwnProfile={isOwnProfile}
                  onEdit={() => setShowProfileEditor(true)}
                />
              )}

              {activeTab === 'photos' && (
                <ProfilePhotos
                  userId={userData.id}
                  isOwnProfile={isOwnProfile}
                />
              )}

              {activeTab === 'connections' && (
                <ProfileConnections
                  userId={userData.id}
                  isOwnProfile={isOwnProfile}
                />
              )}

              {activeTab === 'activity' && (
                <ProfileActivity
                  userId={userData.id}
                  isOwnProfile={isOwnProfile}
                />
              )}

              {activeTab === 'liked' && (
                <ProfileLikedPosts
                  userId={userData.id}
                  isOwnProfile={isOwnProfile}
                />
              )}

              {activeTab === 'blogs' && (
                <ProfileBlogs
                  userId={userData.id}
                  isOwnProfile={isOwnProfile}
                />
              )}

              {activeTab === 'products' && (
                <ProfileProducts
                  userId={userData.id}
                  isOwnProfile={isOwnProfile}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Photo uploaders - only for own profile */}
      {isOwnProfile && (
        <>
          <ProfilePhotoUploader
            isOpen={showProfileUploader}
            onClose={() => setShowProfileUploader(false)}
            currentPhotoUrl={userData.image || null}
            onSuccess={handleProfileUpdate}
          />

          <CoverPhotoUploader
            isOpen={showCoverUploader}
            onClose={() => setShowCoverUploader(false)}
            currentCoverUrl={userData.coverImage || null}
            onSuccess={handleProfileUpdate}
          />

          {/* Profile editor */}
          <ProfileEditor
            isOpen={showProfileEditor}
            onClose={() => setShowProfileEditor(false)}
            userData={userData}
            onSuccess={handleProfileUpdate}
          />
        </>
      )}
    </MainLayout>
  );
}
