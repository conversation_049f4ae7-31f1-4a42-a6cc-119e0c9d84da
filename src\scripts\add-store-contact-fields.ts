import { createConnection } from "mysql2/promise";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

async function main() {
  console.log("Starting database update script...");

  // Create a connection to the database
  const connection = await createConnection({
    host: process.env.DATABASE_HOST,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    port: parseInt(process.env.DATABASE_PORT || '3306'),
  });

  try {
    console.log("Connected to database successfully!");

    // Check if the stores table exists
    const [storesTable] = await connection.query(`
      SHOW TABLES LIKE 'stores'
    `);

    // @ts-ignore
    if (storesTable.length === 0) {
      console.error("Stores table does not exist in the database.");
      return;
    }

    // Check if the contact fields already exist
    const [columns] = await connection.query(`
      SHOW COLUMNS FROM stores LIKE 'phone'
    `);

    // @ts-ignore
    if (columns.length > 0) {
      console.log("Contact fields already exist in the stores table.");
    } else {
      console.log("Adding contact fields to stores table...");

      // Add contact fields to stores table
      await connection.execute(`
        ALTER TABLE stores
        ADD COLUMN phone VARCHAR(50),
        ADD COLUMN email VARCHAR(255),
        ADD COLUMN website VARCHAR(255)
      `);

      console.log("Contact fields added successfully!");
    }

    console.log("Database update completed successfully!");
  } catch (error) {
    console.error("Error updating database:", error);
  } finally {
    await connection.end();
    console.log("Database connection closed.");
  }
}

main().catch(console.error);
