import { createConnection } from 'mysql2/promise';
import { dbConfig } from '../lib/config';

async function main() {
  console.log("Adding foreign key constraint to store_settings table...");
  
  // Create connection
  const connection = await createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });
  
  console.log("Connected to database successfully!");
  
  try {
    // Check if the foreign key already exists
    const [foreignKeys] = await connection.execute(`
      SELECT CONSTRAINT_NAME
      FROM information_schema.TABLE_CONSTRAINTS
      WHERE TABLE_NAME = 'store_settings'
      AND CONSTRAINT_TYPE = 'FOREIGN KEY'
      AND CONSTRAINT_SCHEMA = '${dbConfig.database}'
    `);
    
    // @ts-ignore
    if (foreignKeys.length > 0) {
      console.log("Foreign key constraint already exists on store_settings table.");
    } else {
      console.log("Adding foreign key constraint to store_settings table...");
      
      // First, ensure both columns have the same collation
      await connection.execute(`
        ALTER TABLE store_settings 
        MODIFY storeId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL
      `);
      
      console.log("Updated storeId column collation in store_settings table");
      
      // Add the foreign key constraint
      await connection.execute(`
        ALTER TABLE store_settings
        ADD CONSTRAINT fk_store_settings_store
        FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE
      `);
      
      console.log("Foreign key constraint added successfully!");
    }
  } catch (error) {
    console.error("Error adding foreign key constraint:", error);
  } finally {
    await connection.end();
    console.log("Database connection closed.");
  }
}

main().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});
