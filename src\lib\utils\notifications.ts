interface NotificationData {
  id: string;
  type: string;
  recipientId: string;
  senderId: string;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    image: string;
  };
  postId?: string;
  commentId?: string;
  messageId?: string;
  friendshipId?: string;
  fanPageId?: string;
  fanPagePostId?: string;
  groupId?: string;
  eventId?: string;
  storeId?: string;
  productId?: string;
}

// Placeholder for future real-time notification implementation
export function emitNotification(notification: NotificationData) {
  // TODO: Implement alternative real-time notification system
  console.log('📡 Notification created (real-time disabled):', notification.type);
}

// Helper function to create notification data
export function createNotificationData(
  type: string,
  recipientId: string,
  senderId: string,
  options: {
    postId?: string;
    commentId?: string;
    messageId?: string;
    friendshipId?: string;
    fanPageId?: string;
    fanPagePostId?: string;
    groupId?: string;
    eventId?: string;
    storeId?: string;
    productId?: string;
    sender?: {
      id: string;
      name: string;
      image: string;
    };
  } = {}
): NotificationData {
  return {
    id: '', // Will be set by the database
    type,
    recipientId,
    senderId,
    read: false,
    createdAt: new Date().toISOString(),
    ...options,
  };
}
