"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/Button";
import { UsersIcon, LockClosedIcon, GlobeAltIcon } from "@heroicons/react/24/outline";
import { formatTimeAgo } from "@/lib/utils";
import { useRouter } from "next/navigation";

interface GroupCardProps {
  id: string;
  name: string;
  description: string | null;
  visibility: "public" | "private-visible" | "private-hidden";
  coverImage: string | null;
  category: string | null;
  memberCount: number;
  createdAt: Date;
  userRole: string | null;
}

export function GroupCard({
  id,
  name,
  description,
  visibility,
  coverImage,
  category,
  memberCount,
  createdAt,
  userRole,
}: GroupCardProps) {
  const router = useRouter();
  const [isJoining, setIsJoining] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isPublic = visibility === "public";
  const isPrivateVisible = visibility === "private-visible";
  const isPrivateHidden = visibility === "private-hidden";
  const isMember = userRole === "admin" || userRole === "moderator" || userRole === "member";
  const isPending = userRole === "pending";

  const handleJoinGroup = async () => {
    try {
      setIsJoining(true);
      setError(null);

      const response = await fetch(`/api/groups/${id}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: "current", // The API will use the current user's ID
          action: "request",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to join group");
      }

      // Refresh the page to show updated status
      router.refresh();
    } catch (error) {
      console.error("Error joining group:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsJoining(false);
    }
  };

  return (
    <div className="group overflow-hidden rounded-xl bg-white shadow-md transition-all duration-300 hover:shadow-lg hover:translate-y-[-2px]">
      <Link href={`/groups/${id}`} className="block">
        <div className="relative h-40 w-full bg-gradient-to-r from-blue-500 to-indigo-600 overflow-hidden">
          {coverImage ? (
            <Image
              src={coverImage}
              alt={name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center">
              <UsersIcon className="h-16 w-16 text-white opacity-50" />
            </div>
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-60"></div>

          {/* Badge for visibility */}
          <div className="absolute top-3 left-3 flex items-center rounded-full bg-black/30 backdrop-blur-sm px-2 py-1">
            {isPublic ? (
              <GlobeAltIcon className="h-3.5 w-3.5 text-white" />
            ) : (
              <LockClosedIcon className="h-3.5 w-3.5 text-white" />
            )}
            <span className="ml-1 text-xs font-medium text-white">
              {isPublic ? "Public" : isPrivateVisible ? "Private (Visible)" : "Private (Hidden)"}
            </span>
          </div>

          {/* Category badge */}
          {category && (
            <div className="absolute top-3 right-3 rounded-full bg-white/90 backdrop-blur-sm px-2 py-1">
              <span className="text-xs font-medium text-gray-800">{category}</span>
            </div>
          )}

          {/* Group name overlay */}
          <div className="absolute bottom-0 left-0 right-0 p-4">
            <h3 className="text-xl font-bold text-white line-clamp-1 drop-shadow-sm">{name}</h3>
            <div className="mt-1 flex items-center text-xs text-white/90">
              <span>{memberCount} {memberCount === 1 ? "member" : "members"}</span>
              <span className="mx-1.5">•</span>
              <span>Created {formatTimeAgo(createdAt)}</span>
            </div>
          </div>
        </div>
      </Link>

      <div className="p-4">
        {description && (
          <p className="text-sm text-gray-600 line-clamp-2 mb-4">
            {description}
          </p>
        )}

        <div className="flex items-center justify-between">
          {isMember ? (
            <Button
              variant="outline"
              size="sm"
              className="w-full rounded-full"
              onClick={() => router.push(`/groups/${id}`)}
            >
              View Group
            </Button>
          ) : isPending ? (
            <Button
              variant="outline"
              size="sm"
              className="w-full rounded-full"
              disabled
            >
              Request Pending
            </Button>
          ) : (
            <Button
              size="sm"
              className="w-full rounded-full"
              onClick={handleJoinGroup}
              isLoading={isJoining}
              disabled={isJoining}
            >
              {isPublic ? "Join Group" : "Request to Join"}
            </Button>
          )}
        </div>

        {error && (
          <p className="mt-2 text-xs text-red-500 text-center">{error}</p>
        )}
      </div>
    </div>
  );
}
