import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env' });
dotenv.config({ path: '.env.local' }); // Override with local if exists

async function addIsFromPageField() {
  let connection: mysql.Connection | null = null;

  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST || 'localhost',
      user: process.env.DATABASE_USERNAME || 'root',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || 'hifnf',
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('Connected to database');

    // Check if column already exists
    const [columns] = await connection.query(`
      SHOW COLUMNS FROM fan_page_messages LIKE 'isFromPage'
    `);

    if ((columns as any[]).length === 0) {
      // Add isFromPage column to fan_page_messages table
      await connection.query(`
        ALTER TABLE fan_page_messages
        ADD COLUMN isFromPage BOOLEAN NOT NULL DEFAULT FALSE
        AFTER content
      `);
      console.log('✅ Successfully added isFromPage field to fan_page_messages table');
    } else {
      console.log('ℹ️ isFromPage field already exists in fan_page_messages table');
    }

    // Update existing messages to set isFromPage = false (they are all from users)
    const [result] = await connection.query(`
      UPDATE fan_page_messages
      SET isFromPage = FALSE
      WHERE isFromPage IS NULL
    `);

    console.log('✅ Updated existing messages with isFromPage = false');

  } catch (error) {
    console.error('❌ Error adding isFromPage field:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
addIsFromPageField();
