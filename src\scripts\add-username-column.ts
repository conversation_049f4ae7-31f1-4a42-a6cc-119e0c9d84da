import mysql from "mysql2/promise";
import * as dotenv from "dotenv";

dotenv.config();

async function main() {
  console.log("Adding username column to users table...");

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_drizzle";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  try {
    // Connect to the database
    const connection = await mysql.createConnection({
      host: dbHost,
      user: dbUser,
      password: dbPassword,
      database: dbName,
      port: dbPort,
      multipleStatements: true,
    });

    console.log(`Connected to database '${dbName}'`);
    
    // Check if username column already exists
    const [columns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'username'"
    );
    
    // @ts-ignore
    if (columns.length > 0) {
      console.log("Username column already exists in users table.");
    } else {
      console.log("Adding username column to users table...");
      
      // Add username column
      await connection.execute(
        "ALTER TABLE users ADD COLUMN username VARCHAR(50) UNIQUE AFTER name"
      );
      
      console.log("Username column added successfully!");
    }

    // Close the connection
    await connection.end();
    console.log("Database connection closed.");
  } catch (error) {
    console.error("Error adding username column:", error);
    process.exit(1);
  }
}

main();
