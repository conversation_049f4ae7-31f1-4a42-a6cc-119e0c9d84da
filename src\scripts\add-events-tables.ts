import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

async function main() {
  console.log("Starting events tables creation script...");

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: true } : undefined,
  });

  try {
    console.log("Connected to database successfully!");

    // Check if events table already exists
    const [eventsTable] = await connection.execute(`
      SHOW TABLES LIKE 'events'
    `);

    // @ts-ignore
    if (eventsTable.length > 0) {
      console.log("Events table already exists in database.");
    } else {
      console.log("Creating events table...");

      // First, check the structure of the users table
      const [usersColumns] = await connection.execute(`
        DESCRIBE users
      `);
      console.log("Users table structure:", usersColumns);

      // Create events table without foreign key constraint initially
      await connection.execute(`
        CREATE TABLE events (
          id VARCHAR(255) PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          startTime TIMESTAMP NOT NULL,
          endTime TIMESTAMP NOT NULL,
          location VARCHAR(255),
          isOnline BOOLEAN DEFAULT FALSE,
          onlineLink VARCHAR(255),
          coverImage VARCHAR(255),
          hostId VARCHAR(255) NOT NULL,
          visibility ENUM('public', 'private', 'friends') NOT NULL DEFAULT 'public',
          category VARCHAR(100),
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (hostId)
        )
      `);

      // Try to add foreign key constraint separately
      try {
        await connection.execute(`
          ALTER TABLE events
          ADD CONSTRAINT fk_events_host
          FOREIGN KEY (hostId) REFERENCES users(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for hostId in events table");
      } catch (error) {
        console.error("Error adding hostId foreign key to events table:", error);
      }

      console.log("Events table created successfully!");
    }

    // Check if event_attendees table already exists
    const [eventAttendeesTable] = await connection.execute(`
      SHOW TABLES LIKE 'event_attendees'
    `);

    // @ts-ignore
    if (eventAttendeesTable.length > 0) {
      console.log("Event attendees table already exists in database.");
    } else {
      console.log("Creating event_attendees table...");

      // Create event_attendees table without foreign key constraints initially
      await connection.execute(`
        CREATE TABLE event_attendees (
          id VARCHAR(255) PRIMARY KEY,
          eventId VARCHAR(255) NOT NULL,
          userId VARCHAR(255) NOT NULL,
          status ENUM('going', 'interested', 'not_going') NOT NULL,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (eventId),
          INDEX (userId),
          UNIQUE KEY unique_event_user (eventId, userId)
        )
      `);

      // Try to add foreign key constraints separately
      try {
        await connection.execute(`
          ALTER TABLE event_attendees
          ADD CONSTRAINT fk_event_attendees_event
          FOREIGN KEY (eventId) REFERENCES events(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for eventId in event_attendees table");
      } catch (error) {
        console.error("Error adding eventId foreign key to event_attendees table:", error);
      }

      try {
        await connection.execute(`
          ALTER TABLE event_attendees
          ADD CONSTRAINT fk_event_attendees_user
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for userId in event_attendees table");
      } catch (error) {
        console.error("Error adding userId foreign key to event_attendees table:", error);
      }

      console.log("Event attendees table created successfully!");
    }

    // Check if event_invites table already exists
    const [eventInvitesTable] = await connection.execute(`
      SHOW TABLES LIKE 'event_invites'
    `);

    // @ts-ignore
    if (eventInvitesTable.length > 0) {
      console.log("Event invites table already exists in database.");
    } else {
      console.log("Creating event_invites table...");

      // Create event_invites table without foreign key constraints initially
      await connection.execute(`
        CREATE TABLE event_invites (
          id VARCHAR(255) PRIMARY KEY,
          eventId VARCHAR(255) NOT NULL,
          fromUserId VARCHAR(255) NOT NULL,
          toUserId VARCHAR(255) NOT NULL,
          status ENUM('pending', 'accepted', 'declined') NOT NULL DEFAULT 'pending',
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (eventId),
          INDEX (fromUserId),
          INDEX (toUserId),
          UNIQUE KEY unique_event_invite (eventId, fromUserId, toUserId)
        )
      `);

      // Try to add foreign key constraints separately
      try {
        await connection.execute(`
          ALTER TABLE event_invites
          ADD CONSTRAINT fk_event_invites_event
          FOREIGN KEY (eventId) REFERENCES events(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for eventId in event_invites table");
      } catch (error) {
        console.error("Error adding eventId foreign key to event_invites table:", error);
      }

      try {
        await connection.execute(`
          ALTER TABLE event_invites
          ADD CONSTRAINT fk_event_invites_from_user
          FOREIGN KEY (fromUserId) REFERENCES users(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for fromUserId in event_invites table");
      } catch (error) {
        console.error("Error adding fromUserId foreign key to event_invites table:", error);
      }

      try {
        await connection.execute(`
          ALTER TABLE event_invites
          ADD CONSTRAINT fk_event_invites_to_user
          FOREIGN KEY (toUserId) REFERENCES users(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for toUserId in event_invites table");
      } catch (error) {
        console.error("Error adding toUserId foreign key to event_invites table:", error);
      }

      console.log("Event invites table created successfully!");
    }

    // Check if event_comments table already exists
    const [eventCommentsTable] = await connection.execute(`
      SHOW TABLES LIKE 'event_comments'
    `);

    // @ts-ignore
    if (eventCommentsTable.length > 0) {
      console.log("Event comments table already exists in database.");
    } else {
      console.log("Creating event_comments table...");

      // Create event_comments table without foreign key constraints initially
      await connection.execute(`
        CREATE TABLE event_comments (
          id VARCHAR(255) PRIMARY KEY,
          eventId VARCHAR(255) NOT NULL,
          userId VARCHAR(255) NOT NULL,
          content TEXT NOT NULL,
          images JSON,
          videos JSON,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (eventId),
          INDEX (userId)
        )
      `);

      // Try to add foreign key constraints separately
      try {
        await connection.execute(`
          ALTER TABLE event_comments
          ADD CONSTRAINT fk_event_comments_event
          FOREIGN KEY (eventId) REFERENCES events(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for eventId in event_comments table");
      } catch (error) {
        console.error("Error adding eventId foreign key to event_comments table:", error);
      }

      try {
        await connection.execute(`
          ALTER TABLE event_comments
          ADD CONSTRAINT fk_event_comments_user
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for userId in event_comments table");
      } catch (error) {
        console.error("Error adding userId foreign key to event_comments table:", error);
      }

      console.log("Event comments table created successfully!");
    }

    // Check if notifications table has eventId column
    const [notificationsColumns] = await connection.execute(`
      SHOW COLUMNS FROM notifications LIKE 'eventId'
    `);

    // @ts-ignore
    if (notificationsColumns.length > 0) {
      console.log("eventId column already exists in notifications table.");
    } else {
      console.log("Adding eventId column to notifications table...");

      // Add eventId column to notifications table
      await connection.execute(`
        ALTER TABLE notifications
        ADD COLUMN eventId VARCHAR(255) AFTER groupId
      `);

      // Try to add foreign key constraint
      try {
        await connection.execute(`
          ALTER TABLE notifications
          ADD CONSTRAINT fk_notifications_event
          FOREIGN KEY (eventId) REFERENCES events(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for eventId in notifications table");
      } catch (error) {
        console.error("Error adding eventId foreign key to notifications table:", error);
      }
    }

    console.log("Events tables creation script completed successfully!");
  } catch (error) {
    console.error("Error creating events tables:", error);
  } finally {
    await connection.end();
    console.log("Database connection closed.");
  }
}

main().catch(console.error);
