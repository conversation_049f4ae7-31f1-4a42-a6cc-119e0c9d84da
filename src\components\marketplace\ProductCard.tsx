"use client";

import { useState } from "react";
import Link from "next/link";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { formatDistanceToNow } from "date-fns";
import { BuildingStorefrontIcon, CheckBadgeIcon } from "@heroicons/react/24/outline";

interface ProductCardProps {
  product: {
    id: string;
    title: string;
    price: number;
    condition: string;
    photos: string[] | null;
    createdAt: Date;
    store: {
      id: string;
      name: string;
      logo: string | null;
    } | null;
  };
}

export function ProductCard({ product }: ProductCardProps) {
  const [imageError, setImageError] = useState(false);

  // Format condition for display
  const formatCondition = (condition: string | null | undefined) => {
    if (!condition) return 'Unknown';
    return condition.replace('_', ' ').replace(/\b\w/g, (c) => c.toUpperCase());
  };

  // Format price to currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price); // Price is already in correct format
  };

  return (
    <Link
      href={`/marketplace/product/${product.id}`}
      className="group overflow-hidden rounded-lg bg-white shadow transition-all duration-300 hover:shadow-md flex flex-col h-full"
    >
      {/* Image container with no left/right borders and 100% width */}
      <div className="relative w-full aspect-square overflow-hidden">
        {product.photos && product.photos.length > 0 && !imageError ? (
          <OptimizedImage
            src={product.photos[0]}
            alt={product.title}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
            className="object-cover"
            onError={() => setImageError(true)}
            customPlaceholder="blur"
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center bg-gray-100">
            <span className="text-gray-400">No image available</span>
          </div>
        )}

        {/* Price tag overlay */}
        <div className="absolute bottom-0 right-0 bg-blue-600 text-white px-3 py-1 font-semibold rounded-tl-lg">
          {formatPrice(product.price)}
        </div>
      </div>

      <div className="p-4 flex-1 flex flex-col">
        <h3 className="font-medium text-gray-900 line-clamp-2 mb-2">{product.title}</h3>

        <div className="flex items-center justify-between mb-2 mt-auto">
          <span className="rounded-full bg-gray-100 px-2 py-0.5 text-xs text-gray-700">
            {formatCondition(product.condition)}
          </span>
          <span className="text-xs text-gray-500">
            {formatDistanceToNow(new Date(product.createdAt), { addSuffix: true })}
          </span>
        </div>

        <div className="flex items-center text-xs text-gray-500 pt-2 border-t border-gray-100">
          {product.store?.logo ? (
            <OptimizedImage
              src={product.store.logo}
              alt={product.store?.name || "Store"}
              width={16}
              height={16}
              className="mr-1 rounded-full"
            />
          ) : (
            <BuildingStorefrontIcon className="mr-1 h-3 w-3" />
          )}
          <span>{product.store?.name || "Unknown Store"}</span>
        </div>
      </div>
    </Link>
  );
}
