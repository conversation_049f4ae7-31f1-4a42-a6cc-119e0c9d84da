"use client";

import { Fragment, useState, useRef, useEffect } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon, PhotoIcon, VideoCameraIcon } from "@heroicons/react/24/outline";
import { Button } from "@/components/ui/Button";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { useDropzone } from "react-dropzone";
import { uploadMultipleToCloudinary } from "@/lib/cloudinary";
import { toast } from "react-hot-toast";

interface FanPagePost {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  type: string;
  likeCount: number;
  commentCount: number;
  shareCount: number;
  viewCount: number;
  createdAt: string;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  };
}

interface EditPostModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPostUpdated: (updatedPost: FanPagePost) => void;
  post: FanPagePost;
  pageName: string;
  pageImage?: string | null;
}

export function EditPostModal({
  isOpen,
  onClose,
  onPostUpdated,
  post,
  pageName,
  pageImage,
}: EditPostModalProps) {
  const [content, setContent] = useState(post.content || "");
  const [images, setImages] = useState<File[]>([]);
  const [videos, setVideos] = useState<File[]>([]);
  const [existingImages, setExistingImages] = useState<string[]>(post.images || []);
  const [existingVideos, setExistingVideos] = useState<string[]>(post.videos || []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isOpen) {
      setContent(post.content || "");
      setExistingImages(post.images || []);
      setExistingVideos(post.videos || []);
      setImages([]);
      setVideos([]);
    }
  }, [isOpen, post]);

  const onImageDrop = (acceptedFiles: File[]) => {
    setImages(prev => [...prev, ...acceptedFiles]);
  };

  const onVideoDrop = (acceptedFiles: File[]) => {
    setVideos(prev => [...prev, ...acceptedFiles]);
  };

  const {
    getRootProps: getImageRootProps,
    getInputProps: getImageInputProps,
    isDragActive: isImageDragActive,
  } = useDropzone({
    onDrop: onImageDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    multiple: true,
  });

  const {
    getRootProps: getVideoRootProps,
    getInputProps: getVideoInputProps,
    isDragActive: isVideoDragActive,
  } = useDropzone({
    onDrop: onVideoDrop,
    accept: {
      'video/*': ['.mp4', '.mov', '.avi', '.mkv']
    },
    multiple: true,
  });

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeVideo = (index: number) => {
    setVideos(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingImage = (index: number) => {
    setExistingImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingVideo = (index: number) => {
    setExistingVideos(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!content.trim() && images.length === 0 && videos.length === 0 && existingImages.length === 0 && existingVideos.length === 0) {
      toast.error("Please add some content, images, or videos");
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload new images and videos to Cloudinary
      const newImageUrls: string[] = [];
      const newVideoUrls: string[] = [];

      if (images.length > 0) {
        const uploadedImageUrls = await uploadMultipleToCloudinary(images);
        newImageUrls.push(...uploadedImageUrls);
      }

      if (videos.length > 0) {
        const uploadedVideoUrls = await uploadMultipleToCloudinary(videos);
        newVideoUrls.push(...uploadedVideoUrls);
      }

      // Combine existing and new media
      const allImages = [...existingImages, ...newImageUrls];
      const allVideos = [...existingVideos, ...newVideoUrls];

      // Determine post type
      let postType = 'text';
      if (allImages.length > 0) postType = 'image';
      else if (allVideos.length > 0) postType = 'video';

      const response = await fetch(`/api/fan-pages/posts/${post.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: content.trim(),
          images: allImages,
          videos: allVideos,
          type: postType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update post");
      }

      const data = await response.json();
      onPostUpdated(data.post);
      onClose();
      toast.success("Post updated successfully!");

      // Reset form
      setContent("");
      setImages([]);
      setVideos([]);
      setExistingImages([]);
      setExistingVideos([]);

    } catch (error) {
      console.error("Error updating post:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update post");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex items-center justify-between mb-6">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    Edit Post
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Page Info */}
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="h-12 w-12 rounded-full overflow-hidden">
                      {pageImage ? (
                        <OptimizedImage
                          src={pageImage}
                          alt={pageName}
                          width={48}
                          height={48}
                          className="object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                          <span className="text-lg font-bold text-white">
                            {pageName.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{pageName}</h4>
                      <p className="text-sm text-gray-500">Editing post</p>
                    </div>
                  </div>

                  {/* Content */}
                  <div>
                    <textarea
                      ref={textareaRef}
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="What's on your mind?"
                      className="w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      rows={4}
                    />
                  </div>

                  {/* Existing Images */}
                  {existingImages.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Current Images</h5>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {existingImages.map((image, index) => (
                          <div key={index} className="relative">
                            <OptimizedImage
                              src={image}
                              alt={`Existing image ${index + 1}`}
                              width={200}
                              height={200}
                              className="object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeExistingImage(index)}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Existing Videos */}
                  {existingVideos.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Current Videos</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {existingVideos.map((video, index) => (
                          <div key={index} className="relative">
                            <video
                              src={video}
                              controls
                              className="w-full h-48 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeExistingVideo(index)}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* New Images */}
                  {images.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">New Images</h5>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {images.map((image, index) => (
                          <div key={index} className="relative">
                            <img
                              src={URL.createObjectURL(image)}
                              alt={`New image ${index + 1}`}
                              className="w-full h-32 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeImage(index)}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* New Videos */}
                  {videos.length > 0 && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">New Videos</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {videos.map((video, index) => (
                          <div key={index} className="relative">
                            <video
                              src={URL.createObjectURL(video)}
                              controls
                              className="w-full h-48 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeVideo(index)}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Media Upload Buttons */}
                  <div className="flex space-x-4">
                    <div
                      {...getImageRootProps()}
                      className={`flex-1 border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
                        isImageDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <input {...getImageInputProps()} />
                      <PhotoIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600">
                        {isImageDragActive ? 'Drop images here' : 'Add Images'}
                      </p>
                    </div>

                    <div
                      {...getVideoRootProps()}
                      className={`flex-1 border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
                        isVideoDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <input {...getVideoInputProps()} />
                      <VideoCameraIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600">
                        {isVideoDragActive ? 'Drop videos here' : 'Add Videos'}
                      </p>
                    </div>
                  </div>

                  {/* Submit Buttons */}
                  <div className="flex justify-end space-x-3 pt-4">
                    <Button
                      type="button"
                      onClick={onClose}
                      variant="outline"
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting || (!content.trim() && images.length === 0 && videos.length === 0 && existingImages.length === 0 && existingVideos.length === 0)}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Updating...</span>
                        </div>
                      ) : (
                        "Update Post"
                      )}
                    </Button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
