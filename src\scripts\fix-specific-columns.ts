import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log('Starting migration to fix specific column collations...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
    multipleStatements: true
  });

  try {
    // Set target collation to match users table (utf8mb4_unicode_ci)
    const targetCollation = "utf8mb4_unicode_ci";

    console.log("Step 1: Checking specific column collations...");
    
    // Check collations for the specific columns involved in the error
    const [columnInfo] = await connection.query(`
      SELECT table_name, column_name, collation_name, data_type
      FROM information_schema.COLUMNS
      WHERE table_schema = ? 
      AND ((table_name = 'store_reviews' AND column_name IN ('userId', 'storeId', 'id')) 
           OR (table_name = 'users' AND column_name = 'id'))
      ORDER BY table_name, column_name
    `, [dbName]);
    
    console.log("Column collations:", columnInfo);

    console.log("Step 2: Disabling foreign key checks...");
    await connection.query("SET FOREIGN_KEY_CHECKS = 0");

    // Fix specific columns that are causing the issue
    console.log("Step 3: Fixing store_reviews.userId column...");
    await connection.query(`
      ALTER TABLE store_reviews 
      MODIFY userId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}
    `);
    console.log("✓ store_reviews.userId column fixed");

    console.log("Step 4: Fixing store_reviews.storeId column...");
    await connection.query(`
      ALTER TABLE store_reviews 
      MODIFY storeId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}
    `);
    console.log("✓ store_reviews.storeId column fixed");

    console.log("Step 5: Fixing store_reviews.id column...");
    await connection.query(`
      ALTER TABLE store_reviews 
      MODIFY id VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}
    `);
    console.log("✓ store_reviews.id column fixed");

    // Also fix users.id to make sure it matches
    console.log("Step 6: Fixing users.id column...");
    await connection.query(`
      ALTER TABLE users 
      MODIFY id VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}
    `);
    console.log("✓ users.id column fixed");

    console.log("Step 7: Re-enabling foreign key checks...");
    await connection.query("SET FOREIGN_KEY_CHECKS = 1");

    console.log("✅ Column collation update completed successfully!");
    
    // Verify the fix by checking collations again
    console.log("Verifying column collations...");
    const [verifyColumns] = await connection.query(`
      SELECT table_name, column_name, collation_name, data_type
      FROM information_schema.COLUMNS
      WHERE table_schema = ? 
      AND ((table_name = 'store_reviews' AND column_name IN ('userId', 'storeId', 'id')) 
           OR (table_name = 'users' AND column_name = 'id'))
      ORDER BY table_name, column_name
    `, [dbName]);
    
    console.log("Updated column collations:", verifyColumns);

  } catch (error) {
    console.error("Error during migration:", error);
    
    // Re-enable foreign key checks in case of error
    try {
      await connection.query("SET FOREIGN_KEY_CHECKS = 1");
    } catch (e) {
      console.error("Failed to re-enable foreign key checks:", e);
    }
    
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
