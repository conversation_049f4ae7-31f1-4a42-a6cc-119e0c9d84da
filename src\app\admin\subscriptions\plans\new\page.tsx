"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Select } from "@/components/ui/Select";
import { Checkbox } from "@/components/ui/Checkbox";
import { toast } from "react-hot-toast";
import { ArrowLeftIcon, PlusIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

interface PlanFormData {
  name: string;
  displayName: string;
  description: string;
  price: string;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  features: string[];
  maxPosts: number;
  maxStorage: number;
  maxGroups: number;
  canCreateFanPages: boolean;
  canCreateStores: boolean;
  canMonetizeBlogs: boolean;
  prioritySupport: boolean;
  isActive: boolean;
}

export default function NewSubscriptionPlanPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newFeature, setNewFeature] = useState("");
  const [formData, setFormData] = useState<PlanFormData>({
    name: "",
    displayName: "",
    description: "",
    price: "",
    currency: "USD",
    billingCycle: "monthly",
    features: [],
    maxPosts: -1,
    maxStorage: -1,
    maxGroups: -1,
    canCreateFanPages: false,
    canCreateStores: false,
    canMonetizeBlogs: false,
    prioritySupport: false,
    isActive: true,
  });

  const handleInputChange = (field: keyof PlanFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()],
      }));
      setNewFeature("");
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.displayName.trim() || !formData.price.trim()) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (!/^\d+(\.\d{1,2})?$/.test(formData.price)) {
      toast.error("Please enter a valid price (e.g., 9.99)");
      return;
    }

    try {
      setIsSubmitting(true);
      
      const response = await fetch('/api/admin/subscriptions/plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create subscription plan');
      }

      toast.success('Subscription plan created successfully');
      router.push('/admin/subscriptions/plans');
    } catch (error) {
      console.error('Error creating plan:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create subscription plan');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/admin/subscriptions/plans">
            <Button variant="ghost" size="sm">
              <ArrowLeftIcon className="mr-2 h-4 w-4" />
              Back to Plans
            </Button>
          </Link>
        </div>
        <h1 className="mt-4 text-2xl font-bold text-gray-900">Create Subscription Plan</h1>
        <p className="mt-1 text-sm text-gray-500">
          Set up a new subscription plan with pricing and features
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Plan Name (Internal) *
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., basic, premium, pro"
                required
              />
              <p className="mt-1 text-xs text-gray-500">
                Used internally for identification (lowercase, no spaces)
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Display Name *
              </label>
              <Input
                type="text"
                value={formData.displayName}
                onChange={(e) => handleInputChange('displayName', e.target.value)}
                placeholder="e.g., Basic Plan, Premium Plan"
                required
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe what this plan offers..."
              rows={3}
            />
          </div>
        </Card>

        {/* Pricing */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Pricing</h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price *
              </label>
              <Input
                type="text"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                placeholder="9.99"
                pattern="^\d+(\.\d{1,2})?$"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Currency
              </label>
              <Select
                value={formData.currency}
                onChange={(e) => handleInputChange('currency', e.target.value)}
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="BDT">BDT</option>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Billing Cycle
              </label>
              <Select
                value={formData.billingCycle}
                onChange={(e) => handleInputChange('billingCycle', e.target.value as 'monthly' | 'yearly')}
              >
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </Select>
            </div>
          </div>
        </Card>

        {/* Features */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Features</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Add Feature
              </label>
              <div className="flex space-x-2">
                <Input
                  type="text"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder="Enter a feature..."
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                />
                <Button type="button" onClick={addFeature} variant="outline">
                  <PlusIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
            {formData.features.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Current Features
                </label>
                <div className="space-y-2">
                  {formData.features.map((feature, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="text-sm">{feature}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFeature(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Limits */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Limits</h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Posts
              </label>
              <Input
                type="number"
                value={formData.maxPosts}
                onChange={(e) => handleInputChange('maxPosts', parseInt(e.target.value) || -1)}
                placeholder="-1 for unlimited"
              />
              <p className="mt-1 text-xs text-gray-500">-1 for unlimited</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Storage (MB)
              </label>
              <Input
                type="number"
                value={formData.maxStorage}
                onChange={(e) => handleInputChange('maxStorage', parseInt(e.target.value) || -1)}
                placeholder="-1 for unlimited"
              />
              <p className="mt-1 text-xs text-gray-500">-1 for unlimited</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Groups
              </label>
              <Input
                type="number"
                value={formData.maxGroups}
                onChange={(e) => handleInputChange('maxGroups', parseInt(e.target.value) || -1)}
                placeholder="-1 for unlimited"
              />
              <p className="mt-1 text-xs text-gray-500">-1 for unlimited</p>
            </div>
          </div>
        </Card>

        {/* Permissions */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Permissions</h2>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <Checkbox
              checked={formData.canCreateFanPages}
              onChange={(checked) => handleInputChange('canCreateFanPages', checked)}
              label="Can Create Fan Pages"
            />
            <Checkbox
              checked={formData.canCreateStores}
              onChange={(checked) => handleInputChange('canCreateStores', checked)}
              label="Can Create Stores"
            />
            <Checkbox
              checked={formData.canMonetizeBlogs}
              onChange={(checked) => handleInputChange('canMonetizeBlogs', checked)}
              label="Can Monetize Blogs"
            />
            <Checkbox
              checked={formData.prioritySupport}
              onChange={(checked) => handleInputChange('prioritySupport', checked)}
              label="Priority Support"
            />
          </div>
        </Card>

        {/* Status */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Status</h2>
          <Checkbox
            checked={formData.isActive}
            onChange={(checked) => handleInputChange('isActive', checked)}
            label="Plan is Active"
            description="Inactive plans won't be available for new subscriptions"
          />
        </Card>

        {/* Submit */}
        <div className="flex justify-end space-x-4">
          <Link href="/admin/subscriptions/plans">
            <Button variant="outline" type="button">
              Cancel
            </Button>
          </Link>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Plan"}
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
