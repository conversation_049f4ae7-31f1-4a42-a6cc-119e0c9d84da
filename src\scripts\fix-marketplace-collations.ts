import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log('Starting migration to fix collation issues in marketplace tables...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
    multipleStatements: true
  });

  try {
    // Check current collations
    console.log("Checking current collations...");
    
    const [tables] = await connection.query(
      `SELECT TABLE_NAME, TABLE_COLLATION
       FROM information_schema.TABLES
       WHERE TABLE_SCHEMA = ?`,
      [dbName]
    );
    
    console.log("Tables and their collations:", tables);

    // Get all columns with their collations
    console.log("Getting all columns with their collations...");
    const [columns] = await connection.query(
      `SELECT table_name, column_name, collation_name, data_type
       FROM information_schema.COLUMNS
       WHERE table_schema = ? AND data_type IN ('varchar', 'char', 'text', 'enum')`,
      [dbName]
    );
    
    console.log("Columns and their collations:", columns);

    // Set target collation - we'll use utf8mb4_0900_ai_ci as it's the MySQL 8.0 default
    const targetCollation = "utf8mb4_0900_ai_ci";

    // First, check and drop foreign key constraints on stores table
    console.log("Checking foreign key constraints on stores table...");
    const [foreignKeys] = await connection.query(`
      SELECT CONSTRAINT_NAME
      FROM information_schema.TABLE_CONSTRAINTS
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'stores' AND CONSTRAINT_TYPE = 'FOREIGN KEY'
    `, [dbName]);

    if (Array.isArray(foreignKeys) && foreignKeys.length > 0) {
      console.log("Dropping foreign key constraints on stores table...");
      for (const fk of foreignKeys) {
        // @ts-ignore
        await connection.query(`ALTER TABLE stores DROP FOREIGN KEY ${fk.CONSTRAINT_NAME}`);
        // @ts-ignore
        console.log(`Dropped foreign key constraint: ${fk.CONSTRAINT_NAME}`);
      }
    }

    // Update stores table collation
    console.log(`Setting stores table collation to ${targetCollation}...`);
    await connection.query(
      `ALTER TABLE stores CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );
    console.log("Updated stores table collation");

    // Update specific columns in stores table
    console.log("Updating ownerId column in stores table...");
    await connection.query(`
      ALTER TABLE stores MODIFY ownerId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}
    `);
    console.log("Updated ownerId column in stores table");

    // Check and update products table
    console.log(`Setting products table collation to ${targetCollation}...`);
    await connection.query(
      `ALTER TABLE products CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );
    console.log("Updated products table collation");

    // Update storeId column in products table
    console.log("Updating storeId column in products table...");
    await connection.query(`
      ALTER TABLE products MODIFY storeId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}
    `);
    console.log("Updated storeId column in products table");

    // Check and update store_follows table
    console.log(`Setting store_follows table collation to ${targetCollation}...`);
    await connection.query(
      `ALTER TABLE store_follows CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );
    console.log("Updated store_follows table collation");

    // Update columns in store_follows table
    console.log("Updating columns in store_follows table...");
    await connection.query(`
      ALTER TABLE store_follows MODIFY storeId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation};
      ALTER TABLE store_follows MODIFY userId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}
    `);
    console.log("Updated columns in store_follows table");

    // Check and update store_reviews table
    console.log(`Setting store_reviews table collation to ${targetCollation}...`);
    await connection.query(
      `ALTER TABLE store_reviews CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );
    console.log("Updated store_reviews table collation");

    // Update columns in store_reviews table
    console.log("Updating columns in store_reviews table...");
    await connection.query(`
      ALTER TABLE store_reviews MODIFY storeId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation};
      ALTER TABLE store_reviews MODIFY userId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}
    `);
    console.log("Updated columns in store_reviews table");

    // Re-add foreign key constraints
    console.log("Re-adding foreign key constraints...");
    await connection.query(`
      ALTER TABLE stores
      ADD CONSTRAINT fk_stores_owner
      FOREIGN KEY (ownerId) REFERENCES users(id) ON DELETE CASCADE
    `);
    console.log("Re-added foreign key constraint for stores.ownerId");

    await connection.query(`
      ALTER TABLE products
      ADD CONSTRAINT fk_products_store
      FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE
    `);
    console.log("Re-added foreign key constraint for products.storeId");

    await connection.query(`
      ALTER TABLE store_follows
      ADD CONSTRAINT fk_store_follows_store
      FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE
    `);
    console.log("Re-added foreign key constraint for store_follows.storeId");

    await connection.query(`
      ALTER TABLE store_follows
      ADD CONSTRAINT fk_store_follows_user
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
    `);
    console.log("Re-added foreign key constraint for store_follows.userId");

    await connection.query(`
      ALTER TABLE store_reviews
      ADD CONSTRAINT fk_store_reviews_store
      FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE
    `);
    console.log("Re-added foreign key constraint for store_reviews.storeId");

    await connection.query(`
      ALTER TABLE store_reviews
      ADD CONSTRAINT fk_store_reviews_user
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
    `);
    console.log("Re-added foreign key constraint for store_reviews.userId");

    console.log("Collation update completed successfully!");
  } catch (error) {
    console.error("Error during migration:", error);
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
