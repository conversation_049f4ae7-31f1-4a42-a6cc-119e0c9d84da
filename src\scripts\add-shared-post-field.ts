import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function main() {
  console.log('Starting database migration to add sharedPostId field...');

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
  });

  try {
    // Check if the column already exists
    const [columns] = await connection.execute(`
      SHOW COLUMNS FROM posts LIKE 'sharedPostId'
    `);

    // @ts-ignore
    if (columns.length === 0) {
      console.log('Adding sharedPostId column to posts table...');
      
      // Add the sharedPostId column
      await connection.execute(`
        ALTER TABLE posts
        ADD COLUMN sharedPostId VARCHAR(255) NULL,
        ADD CONSTRAINT fk_posts_shared_post
        FOREIGN KEY (sharedPostId) REFERENCES posts(id)
        ON DELETE SET NULL
      `);
      
      console.log('Successfully added sharedPostId column to posts table');
    } else {
      console.log('sharedPostId column already exists in posts table');
    }
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  } finally {
    await connection.end();
    console.log('Migration completed');
  }
}

main().catch(console.error);
