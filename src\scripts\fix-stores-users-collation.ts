import mysql from "mysql2/promise";
import * as dotenv from "dotenv";

dotenv.config();

async function main() {
  console.log("Starting migration to fix collation issues between stores and users tables...");

  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || "localhost",
    user: process.env.DATABASE_USERNAME || "root",
    password: process.env.DATABASE_PASSWORD || "",
    database: process.env.DATABASE_NAME || "hifnf_db",
    multipleStatements: true,
  });

  try {
    console.log(`Connecting to MySQL at ${process.env.DATABASE_HOST}:${process.env.DATABASE_PORT} as ${process.env.DATABASE_USERNAME}...`);

    // Check current collations
    console.log("Checking current collations...");
    const [storesCollation] = await connection.query(`
      SELECT TABLE_NAME, TABLE_COLLATION
      FROM information_schema.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'stores'
    `, [process.env.DATABASE_NAME]);

    const [usersCollation] = await connection.query(`
      SELECT TABLE_NAME, TABLE_COLLATION
      FROM information_schema.TABLES
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'
    `, [process.env.DATABASE_NAME]);

    console.log("Stores table collation:", storesCollation);
    console.log("Users table collation:", usersCollation);

    // Check for foreign keys on stores table
    console.log("Checking for foreign keys on stores table...");
    const [foreignKeys] = await connection.query(`
      SELECT CONSTRAINT_NAME
      FROM information_schema.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'stores' AND REFERENCED_TABLE_NAME = 'users'
    `, [process.env.DATABASE_NAME]);

    // Drop foreign keys if they exist
    if (Array.isArray(foreignKeys) && foreignKeys.length > 0) {
      console.log("Dropping foreign keys on stores table...");

      for (const fk of foreignKeys) {
        // Use type assertion to handle the dynamic properties
        const typedFk = fk as { CONSTRAINT_NAME: string };
        await connection.query(`
          ALTER TABLE stores DROP FOREIGN KEY ${typedFk.CONSTRAINT_NAME}
        `);
        console.log(`Dropped foreign key: ${typedFk.CONSTRAINT_NAME}`);
      }
    } else {
      console.log("No foreign keys found on stores table.");
    }

    // Update ownerId column in stores table to match users table collation
    console.log("Updating ownerId column in stores table...");
    await connection.query(`
      ALTER TABLE stores MODIFY ownerId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci
    `);
    console.log("Successfully updated ownerId column in stores table.");

    // Re-add foreign key constraint
    if (Array.isArray(foreignKeys) && foreignKeys.length > 0) {
      console.log("Re-adding foreign key constraint...");
      await connection.query(`
        ALTER TABLE stores
        ADD CONSTRAINT fk_stores_owner
        FOREIGN KEY (ownerId) REFERENCES users(id) ON DELETE CASCADE
      `);
      console.log("Successfully re-added foreign key constraint.");
    }

    console.log("Collation update completed successfully!");
  } catch (error) {
    console.error("Error during migration:", error);
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
