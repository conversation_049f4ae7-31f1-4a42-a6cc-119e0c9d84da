import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { v2 as cloudinary } from 'cloudinary';
import { cloudinaryConfig } from "@/lib/config";

// Configure Cloudinary
cloudinary.config({
  cloud_name: cloudinaryConfig.cloudName,
  api_key: cloudinaryConfig.apiKey,
  api_secret: cloudinaryConfig.apiSecret,
});

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the form data from the request
    const formData = await req.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { message: "No files provided" },
        { status: 400 }
      );
    }

    // Validate files
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      if (!file || file.size === 0) {
        return NextResponse.json(
          { message: `File ${i + 1} is empty or invalid` },
          { status: 400 }
        );
      }

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        return NextResponse.json(
          { message: `File ${file.name} is too large. Maximum size is 10MB` },
          { status: 400 }
        );
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        return NextResponse.json(
          { message: `File ${file.name} has invalid type. Only images are allowed` },
          { status: 400 }
        );
      }
    }

    // Upload each file to Cloudinary
    const uploadPromises = files.map(async (file, index) => {
      try {
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);

        // Convert buffer to base64
        const base64 = buffer.toString('base64');
        const dataURI = `data:${file.type};base64,${base64}`;

        // Upload to Cloudinary
        const result = await new Promise<string>((resolve, reject) => {
          cloudinary.uploader.upload(
            dataURI,
            {
              folder: 'hifnf_storage',
              resource_type: 'auto',
              transformation: [
                { quality: 'auto:good' },
                { fetch_format: 'auto' }
              ]
            },
            (error, result) => {
              if (error) {
                reject(new Error(`Cloudinary upload failed: ${error.message}`));
              } else if (!result?.secure_url) {
                reject(new Error('No secure URL returned from Cloudinary'));
              } else {
                resolve(result.secure_url);
              }
            }
          );
        });

        return result;

      } catch (error) {
        throw new Error(`Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    });

    // Wait for all uploads to complete
    const uploadedUrls = await Promise.all(uploadPromises);

    // Validate all URLs
    const validUrls = uploadedUrls.filter(url => url && typeof url === 'string' && url.trim().length > 0);

    if (validUrls.length !== files.length) {
      return NextResponse.json(
        { message: `Upload validation failed: Expected ${files.length} URLs, got ${validUrls.length}` },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { urls: validUrls },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      { message: `Error uploading files: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
