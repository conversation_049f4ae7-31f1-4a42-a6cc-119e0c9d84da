"use client";

import { useState, useEffect } from "react";
import { messagePerformanceMonitor } from "@/lib/monitoring/messagePerformance";
import { messageCache } from "@/lib/cache/messageCache";
import { cn } from "@/lib/utils";

interface PerformanceStats {
  messageDelivery: {
    averageDeliveryTime: number;
    successRate: number;
    retryRate: number;
    optimisticAccuracy: number;
    totalMessages: number;
  };
  cachePerformance: {
    hitRate: number;
    missRate: number;
    averageResponseTime: number;
    memoryUsage: number;
    totalRequests: number;
  };
  connectionQuality: {
    latency: number;
    packetLoss: number;
    bandwidth: number;
    stability: string;
    lastUpdated: number;
  };
  overallHealth: string;
}

export function PerformanceDashboard({ 
  isOpen, 
  onClose 
}: { 
  isOpen: boolean; 
  onClose: () => void; 
}) {
  const [stats, setStats] = useState<PerformanceStats | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isOpen) {
      // Initial load
      updateStats();
      
      // Set up auto-refresh
      const interval = setInterval(updateStats, 2000);
      setRefreshInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [isOpen]);

  const updateStats = () => {
    const performanceStats = messagePerformanceMonitor.getPerformanceSummary();
    const cacheStats = messageCache.getStats();
    // Firebase connection manager removed - real-time features disabled
    const connectionStatus = { isConnected: false, activeConnections: 0, lastConnected: null };

    setStats({
      messageDelivery: performanceStats.messageDelivery,
      cachePerformance: {
        hitRate: cacheStats.hitRate,
        missRate: cacheStats.missRate,
        averageResponseTime: 0, // Would need to implement
        memoryUsage: cacheStats.memoryUsage,
        totalRequests: cacheStats.totalConversations,
      },
      connectionQuality: performanceStats.connectionQuality,
      overallHealth: performanceStats.overallHealth,
    });
  };

  if (!isOpen || !stats) return null;

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-green-600 bg-green-50';
      case 'good': return 'text-blue-600 bg-blue-50';
      case 'fair': return 'text-yellow-600 bg-yellow-50';
      case 'poor': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Performance Dashboard</h2>
            <p className="text-gray-600">Real-time messaging performance metrics</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Overall Health */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Overall Health</h3>
            <div className={cn(
              "px-4 py-2 rounded-full text-sm font-medium capitalize",
              getHealthColor(stats.overallHealth)
            )}>
              {stats.overallHealth}
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Message Delivery */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-3">Message Delivery</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Success Rate:</span>
                <span className="font-medium">{formatPercentage(stats.messageDelivery.successRate)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Avg Delivery:</span>
                <span className="font-medium">{formatTime(stats.messageDelivery.averageDeliveryTime)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Retry Rate:</span>
                <span className="font-medium">{formatPercentage(stats.messageDelivery.retryRate)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Optimistic Accuracy:</span>
                <span className="font-medium">{formatPercentage(stats.messageDelivery.optimisticAccuracy)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Messages:</span>
                <span className="font-medium">{stats.messageDelivery.totalMessages}</span>
              </div>
            </div>
          </div>

          {/* Cache Performance */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-3">Cache Performance</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Hit Rate:</span>
                <span className="font-medium">{formatPercentage(stats.cachePerformance.hitRate)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Miss Rate:</span>
                <span className="font-medium">{formatPercentage(stats.cachePerformance.missRate)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Memory Usage:</span>
                <span className="font-medium">{formatBytes(stats.cachePerformance.memoryUsage)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Requests:</span>
                <span className="font-medium">{stats.cachePerformance.totalRequests}</span>
              </div>
            </div>
          </div>

          {/* Connection Quality */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-3">Connection Quality</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Latency:</span>
                <span className="font-medium">{formatTime(stats.connectionQuality.latency)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Packet Loss:</span>
                <span className="font-medium">{formatPercentage(stats.connectionQuality.packetLoss)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Bandwidth:</span>
                <span className="font-medium">{stats.connectionQuality.bandwidth} Mbps</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Stability:</span>
                <span className={cn(
                  "font-medium capitalize",
                  getHealthColor(stats.connectionQuality.stability).split(' ')[0]
                )}>
                  {stats.connectionQuality.stability}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => messageCache.clear()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Clear Cache
            </button>
            <button
              onClick={() => messagePerformanceMonitor.clearOldMetrics()}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Clear Old Metrics
            </button>
            <button
              onClick={() => {
                const data = messagePerformanceMonitor.exportMetrics();
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `performance-metrics-${Date.now()}.json`;
                a.click();
                URL.revokeObjectURL(url);
              }}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Export Metrics
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Performance indicator component for the main UI
export function PerformanceIndicator({ onClick }: { onClick: () => void }) {
  const [health, setHealth] = useState<string>('good');

  useEffect(() => {
    const updateHealth = () => {
      const summary = messagePerformanceMonitor.getPerformanceSummary();
      setHealth(summary.overallHealth);
    };

    updateHealth();
    const interval = setInterval(updateHealth, 5000);

    return () => clearInterval(interval);
  }, []);

  const getIndicatorColor = () => {
    switch (health) {
      case 'excellent': return 'bg-green-500';
      case 'good': return 'bg-blue-500';
      case 'fair': return 'bg-yellow-500';
      case 'poor': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <button
      onClick={onClick}
      className="fixed bottom-4 left-4 z-40 p-3 bg-white rounded-full shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-200"
      title={`Performance: ${health}`}
    >
      <div className="flex items-center space-x-2">
        <div className={cn("w-3 h-3 rounded-full", getIndicatorColor())} />
        <span className="text-sm font-medium text-gray-700 capitalize">{health}</span>
      </div>
    </button>
  );
}
