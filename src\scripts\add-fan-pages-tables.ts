import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import { dbConfig } from '../lib/config';

async function addFanPagesTables() {
  const connection = await mysql.createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });

  try {
    console.log('Adding Fan Pages tables...');

    // Fan Pages table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS fan_pages (
        id VARCHAR(255) PRIMARY KEY,
        ownerId VARCHAR(255) NOT NULL,
        name VARCHA<PERSON>(255) NOT NULL,
        username VARCHAR(50) NOT NULL UNIQUE,
        category ENUM(
          'musician', 'actor', 'brand', 'business', 'organization',
          'public_figure', 'artist', 'writer', 'athlete', 'politician',
          'entertainment', 'media', 'community', 'cause', 'other'
        ) NOT NULL,
        description TEXT,
        profileImage VARCHAR(255),
        coverImage VARCHAR(255),
        website VARCHAR(255),
        email VARCHAR(255),
        phone VARCHAR(50),
        location VARCHAR(255),
        isVerified BOOLEAN DEFAULT FALSE,
        isActive BOOLEAN DEFAULT TRUE,
        followerCount INT DEFAULT 0,
        postCount INT DEFAULT 0,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
        FOREIGN KEY (ownerId) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    // Fan Page Followers table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS fan_page_followers (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL,
        fanPageId VARCHAR(255) NOT NULL,
        followedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (fanPageId) REFERENCES fan_pages(id) ON DELETE CASCADE,
        UNIQUE KEY unique_follow (userId, fanPageId)
      )
    `);

    // Fan Page Posts table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS fan_page_posts (
        id VARCHAR(255) PRIMARY KEY,
        fanPageId VARCHAR(255) NOT NULL,
        content TEXT,
        images JSON,
        videos JSON,
        type ENUM('text', 'image', 'video', 'link', 'event') DEFAULT 'text' NOT NULL,
        scheduledAt TIMESTAMP NULL,
        isPublished BOOLEAN DEFAULT TRUE,
        likeCount INT DEFAULT 0,
        commentCount INT DEFAULT 0,
        shareCount INT DEFAULT 0,
        viewCount INT DEFAULT 0,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
        FOREIGN KEY (fanPageId) REFERENCES fan_pages(id) ON DELETE CASCADE
      )
    `);

    // Fan Page Post Likes table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS fan_page_post_likes (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL,
        fanPagePostId VARCHAR(255) NOT NULL,
        type ENUM('like', 'love', 'wow', 'haha', 'sad', 'angry') DEFAULT 'like' NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (fanPagePostId) REFERENCES fan_page_posts(id) ON DELETE CASCADE,
        UNIQUE KEY unique_like (userId, fanPagePostId)
      )
    `);

    // Fan Page Post Comments table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS fan_page_post_comments (
        id VARCHAR(255) PRIMARY KEY,
        fanPagePostId VARCHAR(255) NOT NULL,
        userId VARCHAR(255),
        fanPageId VARCHAR(255),
        content TEXT NOT NULL,
        parentId VARCHAR(255),
        likeCount INT DEFAULT 0,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
        FOREIGN KEY (fanPagePostId) REFERENCES fan_page_posts(id) ON DELETE CASCADE,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (fanPageId) REFERENCES fan_pages(id) ON DELETE CASCADE,
        FOREIGN KEY (parentId) REFERENCES fan_page_post_comments(id) ON DELETE CASCADE
      )
    `);

    // Fan Page Roles table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS fan_page_roles (
        id VARCHAR(255) PRIMARY KEY,
        fanPageId VARCHAR(255) NOT NULL,
        userId VARCHAR(255) NOT NULL,
        role ENUM('admin', 'editor', 'moderator') NOT NULL,
        addedBy VARCHAR(255) NOT NULL,
        permissions JSON,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
        FOREIGN KEY (fanPageId) REFERENCES fan_pages(id) ON DELETE CASCADE,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (addedBy) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_role (fanPageId, userId)
      )
    `);

    // Fan Page Settings table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS fan_page_settings (
        id VARCHAR(255) PRIMARY KEY,
        fanPageId VARCHAR(255) NOT NULL UNIQUE,
        messagingEnabled BOOLEAN DEFAULT TRUE,
        postVisibility ENUM('public', 'followers') DEFAULT 'public',
        allowComments BOOLEAN DEFAULT TRUE,
        allowSharing BOOLEAN DEFAULT TRUE,
        moderateComments BOOLEAN DEFAULT FALSE,
        autoReply TEXT,
        emailNotifications BOOLEAN DEFAULT TRUE,
        followerNotifications BOOLEAN DEFAULT TRUE,
        postNotifications BOOLEAN DEFAULT TRUE,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
        FOREIGN KEY (fanPageId) REFERENCES fan_pages(id) ON DELETE CASCADE
      )
    `);

    // Update notifications table to include fan page fields
    await connection.execute(`
      ALTER TABLE notifications
      MODIFY COLUMN type ENUM(
        'like', 'comment', 'friend_request', 'friend_accept', 'message',
        'group_invite', 'group_join_request', 'group_join_approved', 'group_post', 'group_announcement',
        'event_invite', 'event_reminder', 'event_update', 'event_comment',
        'store_follow', 'store_review', 'product_new', 'product_report',
        'fan_page_follow', 'fan_page_post', 'fan_page_comment', 'fan_page_like', 'fan_page_role_added'
      ) NOT NULL
    `);

    // Add fan page columns to notifications table (check if they exist first)
    try {
      await connection.execute(`
        ALTER TABLE notifications
        ADD COLUMN fanPageId VARCHAR(255)
      `);
    } catch (error: any) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }

    try {
      await connection.execute(`
        ALTER TABLE notifications
        ADD COLUMN fanPagePostId VARCHAR(255)
      `);
    } catch (error: any) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }

    console.log('✅ Fan Pages tables created successfully!');

  } catch (error) {
    console.error('❌ Error creating Fan Pages tables:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run the script
addFanPagesTables()
  .then(() => {
    console.log('Fan Pages tables setup completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Failed to setup Fan Pages tables:', error);
    process.exit(1);
  });
