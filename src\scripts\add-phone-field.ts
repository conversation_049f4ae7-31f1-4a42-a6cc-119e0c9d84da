import mysql from 'mysql2/promise';
import { config } from 'dotenv';

config();

async function addPhoneField() {
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || 'localhost',
    user: process.env.DATABASE_USERNAME || 'root',
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME || 'hifnf_db',
  });

  try {
    console.log('🔄 Adding phone field to users table...');

    // Add phone field to users table
    await connection.query(`
      ALTER TABLE users
      ADD COLUMN phone VARCHAR(20) NULL
      AFTER email
    `);

    console.log('✅ Phone field added to users table successfully');

  } catch (error: any) {
    if (error.code === 'ER_DUP_FIELDNAME') {
      console.log('ℹ️ Phone field already exists in users table');
    } else {
      console.error('❌ Error adding phone field:', error);
      throw error;
    }
  } finally {
    await connection.end();
  }
}

addPhoneField().catch(console.error);
