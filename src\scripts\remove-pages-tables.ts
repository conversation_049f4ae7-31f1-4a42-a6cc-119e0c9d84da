import { createConnection } from 'mysql2/promise';
import { dbConfig } from '../lib/config';

async function main() {
  console.log("Removing pages-related tables and columns...");
  
  // Create connection
  const connection = await createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });
  
  console.log("Connected to database successfully!");
  
  try {
    // Remove pageId column from posts table
    console.log("Removing pageId column from posts table...");
    try {
      await connection.execute(`
        ALTER TABLE posts DROP COLUMN pageId
      `);
      console.log("Successfully removed pageId column from posts table");
    } catch (error) {
      console.log("pageId column might not exist in posts table or already removed");
    }
    
    // Drop pageLikes table
    console.log("Dropping pageLikes table...");
    try {
      await connection.execute(`DROP TABLE IF EXISTS pageLikes`);
      console.log("Successfully dropped pageLikes table");
    } catch (error) {
      console.log("pageLikes table might not exist or already removed");
    }
    
    // Drop pages table
    console.log("Dropping pages table...");
    try {
      await connection.execute(`DROP TABLE IF EXISTS pages`);
      console.log("Successfully dropped pages table");
    } catch (error) {
      console.log("pages table might not exist or already removed");
    }
    
    console.log("Pages-related tables and columns removed successfully!");
    
  } catch (error) {
    console.error("Error removing pages-related tables:", error);
  } finally {
    await connection.end();
  }
}

main().catch(console.error);
