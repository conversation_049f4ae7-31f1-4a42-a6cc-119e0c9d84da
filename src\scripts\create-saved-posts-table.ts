import mysql from "mysql2/promise";
import * as dotenv from "dotenv";

dotenv.config();

async function main() {
  console.log("Creating savedPosts table...");

  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || "localhost",
    user: process.env.DATABASE_USERNAME || "root",
    password: process.env.DATABASE_PASSWORD || "",
    database: process.env.DATABASE_NAME || "hifnf_db",
    multipleStatements: true,
  });

  try {
    // Check if the table already exists
    const [tables] = await connection.query(`
      SHOW TABLES LIKE 'savedPosts'
    `);

    // @ts-ignore
    if (tables.length > 0) {
      console.log("savedPosts table already exists.");
      return;
    }

    // Create the savedPosts table
    await connection.query(`
      CREATE TABLE savedPosts (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL,
        postId VARCHAR(255) NOT NULL,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        INDEX (userId),
        INDEX (postId)
      )
    `);

    console.log("savedPosts table created successfully!");
  } catch (error) {
    console.error("Error creating savedPosts table:", error);
  } finally {
    await connection.end();
  }
}

main().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});
