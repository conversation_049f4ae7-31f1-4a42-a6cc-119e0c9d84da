import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { stores, storeReviews, users, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, and, desc, count, avg } from "drizzle-orm";

const reviewSchema = z.object({
  rating: z.number().min(1).max(5),
  comment: z.string().max(1000).optional(),
});

export async function GET(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const page = parseInt(searchParams.get("page") || "1");
    const offset = (page - 1) * limit;
    const params = await context.params;
    const storeId = params.storeId;

    // Check if store exists
    const existingStore = await db
      .select({ id: stores.id })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Get reviews
    const reviews = await db
      .select({
        id: storeReviews.id,
        rating: storeReviews.rating,
        comment: storeReviews.comment,
        createdAt: storeReviews.createdAt,
        user: {
          id: users.id,
          name: users.name,
          image: users.image,
        },
      })
      .from(storeReviews)
      .leftJoin(users, eq(storeReviews.userId, users.id))
      .where(eq(storeReviews.storeId, storeId))
      .orderBy(desc(storeReviews.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: count() })
      .from(storeReviews)
      .where(eq(storeReviews.storeId, storeId));

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Get average rating
    const averageRatingResult = await db
      .select({ average: avg(storeReviews.rating) })
      .from(storeReviews)
      .where(eq(storeReviews.storeId, storeId));

    return NextResponse.json({
      reviews,
      pagination: {
        total: totalCount,
        totalPages,
        currentPage: page,
        limit,
      },
      averageRating: averageRatingResult[0]?.average || 0,
    });
  } catch (error) {
    console.error("Error fetching reviews:", error);
    return NextResponse.json(
      { message: "Error fetching reviews" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: Request,
  context: { params: Promise<{ storeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const params = await context.params;
    const storeId = params.storeId;
    const body = await request.json();
    const validatedData = reviewSchema.parse(body);

    // Check if store exists
    const existingStore = await db
      .select({
        id: stores.id,
        ownerId: stores.ownerId,
      })
      .from(stores)
      .where(eq(stores.id, storeId))
      .limit(1);

    if (existingStore.length === 0) {
      return NextResponse.json(
        { message: "Store not found" },
        { status: 404 }
      );
    }

    // Check if user is the store owner (can't review own store)
    if (existingStore[0].ownerId === session.user.id) {
      return NextResponse.json(
        { message: "You cannot review your own store" },
        { status: 400 }
      );
    }

    // Check if user already reviewed this store
    const existingReview = await db
      .select()
      .from(storeReviews)
      .where(
        and(
          eq(storeReviews.storeId, storeId),
          eq(storeReviews.userId, session.user.id)
        )
      )
      .limit(1);

    // If review exists, update it
    if (existingReview.length > 0) {
      await db
        .update(storeReviews)
        .set({
          rating: validatedData.rating,
          comment: validatedData.comment || null,
          updatedAt: new Date(),
        })
        .where(eq(storeReviews.id, existingReview[0].id));

      return NextResponse.json(
        { message: "Review updated successfully" },
        { status: 200 }
      );
    }

    // Create new review
    const reviewId = uuidv4();
    await db.insert(storeReviews).values({
      id: reviewId,
      storeId,
      userId: session.user.id,
      rating: validatedData.rating,
      comment: validatedData.comment || null,
    });

    // Create notification for store owner
    const notificationId = uuidv4();
    await db.insert(notifications).values({
      id: notificationId,
      recipientId: existingStore[0].ownerId,
      type: "store_review",
      senderId: session.user.id,
      storeId,
      read: false,
    });

    return NextResponse.json(
      { message: "Review submitted successfully" },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error submitting review:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Error submitting review" },
      { status: 500 }
    );
  }
}
