import mysql from "mysql2/promise";
import * as dotenv from "dotenv";

dotenv.config();

async function main() {
  console.log("Setting up marketplace tables...");

  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || "localhost",
    user: process.env.DATABASE_USERNAME || "root",
    password: process.env.DATABASE_PASSWORD || "",
    database: process.env.DATABASE_NAME || "hifnf_db",
    multipleStatements: true,
  });

  try {
    // Check if stores table already exists
    const [storesTable] = await connection.execute(
      "SHOW TABLES LIKE 'stores'"
    );
    
    // @ts-ignore
    if (storesTable.length > 0) {
      console.log("Stores table already exists in database.");
    } else {
      console.log("Creating stores table...");
      
      // Create stores table
      await connection.execute(`
        CREATE TABLE stores (
          id VARCHAR(255) PRIMARY KEY,
          name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
          slug VARCHAR(100) NOT NULL UNIQUE,
          description TEXT,
          logo VARCHAR(255),
          banner VARCHAR(255),
          location VARCHAR(255),
          ownerId VARCHAR(255) NOT NULL,
          isVerified BOOLEAN DEFAULT FALSE,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (ownerId) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      
      console.log("Stores table created successfully!");
    }

    // Check if store_follows table already exists
    const [storeFollowsTable] = await connection.execute(
      "SHOW TABLES LIKE 'store_follows'"
    );
    
    // @ts-ignore
    if (storeFollowsTable.length > 0) {
      console.log("Store follows table already exists in database.");
    } else {
      console.log("Creating store_follows table...");
      
      // Create store_follows table
      await connection.execute(`
        CREATE TABLE store_follows (
          id VARCHAR(255) PRIMARY KEY,
          userId VARCHAR(255) NOT NULL,
          storeId VARCHAR(255) NOT NULL,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE,
          UNIQUE KEY unique_store_follow (userId, storeId)
        )
      `);
      
      console.log("Store follows table created successfully!");
    }

    // Check if products table already exists
    const [productsTable] = await connection.execute(
      "SHOW TABLES LIKE 'products'"
    );
    
    // @ts-ignore
    if (productsTable.length > 0) {
      console.log("Products table already exists in database.");
    } else {
      console.log("Creating products table...");
      
      // Create products table
      await connection.execute(`
        CREATE TABLE products (
          id VARCHAR(255) PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          price INT NOT NULL,
          condition ENUM('new', 'like_new', 'good', 'fair', 'poor') NOT NULL,
          category VARCHAR(100) NOT NULL,
          location VARCHAR(255),
          photos JSON,
          storeId VARCHAR(255) NOT NULL,
          viewCount INT DEFAULT 0,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE
        )
      `);
      
      console.log("Products table created successfully!");
    }

    // Check if store_reviews table already exists
    const [storeReviewsTable] = await connection.execute(
      "SHOW TABLES LIKE 'store_reviews'"
    );
    
    // @ts-ignore
    if (storeReviewsTable.length > 0) {
      console.log("Store reviews table already exists in database.");
    } else {
      console.log("Creating store_reviews table...");
      
      // Create store_reviews table
      await connection.execute(`
        CREATE TABLE store_reviews (
          id VARCHAR(255) PRIMARY KEY,
          userId VARCHAR(255) NOT NULL,
          storeId VARCHAR(255) NOT NULL,
          rating INT NOT NULL,
          comment TEXT,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE
        )
      `);
      
      console.log("Store reviews table created successfully!");
    }

    // Check if notifications table has storeId and productId columns
    const [storeIdColumn] = await connection.execute(
      "SHOW COLUMNS FROM notifications LIKE 'storeId'"
    );
    
    // @ts-ignore
    if (storeIdColumn.length > 0) {
      console.log("storeId column already exists in notifications table.");
    } else {
      console.log("Adding storeId column to notifications table...");
      
      // Add storeId column to notifications table
      await connection.execute(`
        ALTER TABLE notifications 
        ADD COLUMN storeId VARCHAR(255) AFTER eventId
      `);
      
      console.log("storeId column added to notifications table successfully!");
    }

    const [productIdColumn] = await connection.execute(
      "SHOW COLUMNS FROM notifications LIKE 'productId'"
    );
    
    // @ts-ignore
    if (productIdColumn.length > 0) {
      console.log("productId column already exists in notifications table.");
    } else {
      console.log("Adding productId column to notifications table...");
      
      // Add productId column to notifications table
      await connection.execute(`
        ALTER TABLE notifications 
        ADD COLUMN productId VARCHAR(255) AFTER storeId
      `);
      
      console.log("productId column added to notifications table successfully!");
    }

    // Check if notification type enum includes marketplace types
    const [typeColumn] = await connection.execute(
      "SHOW COLUMNS FROM notifications LIKE 'type'"
    );
    
    // @ts-ignore
    const typeEnum = typeColumn[0].Type.toString().match(/enum\((.*)\)/i)[1].split(',').map(type => 
      type.replace(/'/g, '').trim()
    );
    
    const marketplaceTypes = ['store_follow', 'store_product', 'store_review'];
    const missingTypes = marketplaceTypes.filter(type => !typeEnum.includes(type));
    
    if (missingTypes.length > 0) {
      console.log("Updating notifications table to include marketplace notification types...");
      
      // Create a new enum string with all existing types plus the new ones
      const newTypeEnum = [...typeEnum, ...missingTypes].map(type => `'${type}'`).join(', ');
      
      // Update the ENUM type to include marketplace-related notification types
      await connection.execute(`
        ALTER TABLE notifications
        MODIFY COLUMN type ENUM(${newTypeEnum}) NOT NULL
      `);
      
      console.log("Updated notification types enum with marketplace types");
    } else {
      console.log("Notification types already include marketplace types");
    }

    console.log("Marketplace tables setup complete!");
  } catch (error) {
    console.error("Error setting up marketplace tables:", error);
    process.exit(1);
  } finally {
    await connection.end();
  }
}

main().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});
