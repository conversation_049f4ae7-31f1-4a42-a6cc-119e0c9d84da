import { exec } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';

const execAsync = promisify(exec);

async function main() {
  try {
    console.log('Running store settings table migration...');
    
    // Get the absolute path to the script
    const scriptPath = path.resolve(__dirname, 'add-store-settings-table.ts');
    
    // Run the script using ts-node
    const { stdout, stderr } = await execAsync(`npx ts-node ${scriptPath}`);
    
    if (stdout) {
      console.log(stdout);
    }
    
    if (stderr) {
      console.error(stderr);
    }
    
    console.log('Store settings table migration completed successfully!');
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

main();
