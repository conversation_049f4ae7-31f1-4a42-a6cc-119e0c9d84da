import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log("Connecting to database...");
  
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
  });
  
  console.log("Connected to database successfully!");
  
  // Check if store_reviews table exists
  const [storeReviewsTable] = await connection.execute(`
    SHOW TABLES LIKE 'store_reviews'
  `);
  
  // @ts-ignore
  if (storeReviewsTable.length === 0) {
    console.log("Store reviews table does not exist in database.");
  } else {
    console.log("Checking for isApproved and isReported columns...");
    
    // Check if isApproved column exists
    const [isApprovedColumn] = await connection.execute(`
      SHOW COLUMNS FROM store_reviews LIKE 'isApproved'
    `);
    
    // @ts-ignore
    if (isApprovedColumn.length === 0) {
      console.log("Adding isApproved column to store_reviews table...");
      
      await connection.execute(`
        ALTER TABLE store_reviews
        ADD COLUMN isApproved BOOLEAN DEFAULT TRUE
      `);
      
      console.log("isApproved column added successfully!");
    } else {
      console.log("isApproved column already exists.");
    }
    
    // Check if isReported column exists
    const [isReportedColumn] = await connection.execute(`
      SHOW COLUMNS FROM store_reviews LIKE 'isReported'
    `);
    
    // @ts-ignore
    if (isReportedColumn.length === 0) {
      console.log("Adding isReported column to store_reviews table...");
      
      await connection.execute(`
        ALTER TABLE store_reviews
        ADD COLUMN isReported BOOLEAN DEFAULT FALSE
      `);
      
      console.log("isReported column added successfully!");
    } else {
      console.log("isReported column already exists.");
    }
  }
  
  await connection.end();
  console.log("Database connection closed.");
}

main().catch((error) => {
  console.error("Error updating store_reviews table:", error);
  process.exit(1);
});
