"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Label } from "@/components/ui/Label";
import { Switch } from "@/components/ui/Switch";
import { Spinner } from "@/components/ui/Spinner";
import { CommissionAnalytics } from "@/components/admin/CommissionAnalytics";
import { CommissionSettingsValidator } from "@/components/admin/CommissionSettingsValidator";
import { toast } from "react-hot-toast";
import {
  Cog6ToothIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ShieldCheckIcon,
  ArrowPathIcon,
  PlusIcon,
  TrashIcon,
  PencilIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ChartBarIcon,
  CalendarIcon,
  BanknotesIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  DocumentArrowDownIcon,
  QuestionMarkCircleIcon,
  BookOpenIcon
} from "@heroicons/react/24/outline";

interface CommissionSettings {
  id: string;
  isEnabled: boolean;
  firstPurchaseEnabled: boolean;
  recurringPurchaseEnabled: boolean;
  defaultCommissionType: 'fixed' | 'percentage';
  defaultCommissionValue: string;
  defaultRecurringCommissionValue: string;
  maxCommissionAmount: string;
  eligibilityPeriodDays: number;
  minSubscriptionAmount: string;
  requireActiveReferrer: boolean;
  maxCommissionsPerReferral: number;
}

interface PackageCommissionSetting {
  id: string;
  planId: string;
  planName: string;
  isEnabled: boolean;
  commissionType: 'fixed' | 'percentage';
  firstPurchaseCommission: string;
  recurringCommission: string;
  maxCommissionAmount?: string;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  price: string;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  isActive: boolean;
}

export default function CommissionSettingsPage() {
  const [settings, setSettings] = useState<CommissionSettings | null>(null);
  const [packageSettings, setPackageSettings] = useState<PackageCommissionSetting[]>([]);
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddPackage, setShowAddPackage] = useState(false);
  const [editingPackage, setEditingPackage] = useState<PackageCommissionSetting | null>(null);
  const [newPackageSetting, setNewPackageSetting] = useState<Partial<PackageCommissionSetting>>({
    planId: '',
    planName: '',
    isEnabled: true,
    commissionType: 'percentage',
    firstPurchaseCommission: '10.00',
    recurringCommission: '5.00',
    maxCommissionAmount: ''
  });
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [showPreview, setShowPreview] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [analyticsRefreshTrigger, setAnalyticsRefreshTrigger] = useState(0);
  const [showValidator, setShowValidator] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  // Validation functions
  const validatePackageSetting = (setting: Partial<PackageCommissionSetting>): Record<string, string> => {
    const errors: Record<string, string> = {};

    if (!setting.planId) {
      errors.planId = 'Please select a subscription plan';
    }

    if (!setting.firstPurchaseCommission || parseFloat(setting.firstPurchaseCommission) < 0) {
      errors.firstPurchaseCommission = 'First purchase commission must be a positive number';
    }

    if (setting.commissionType === 'percentage') {
      if (parseFloat(setting.firstPurchaseCommission || '0') > 100) {
        errors.firstPurchaseCommission = 'Percentage cannot exceed 100%';
      }
      if (parseFloat(setting.recurringCommission || '0') > 100) {
        errors.recurringCommission = 'Percentage cannot exceed 100%';
      }
    }

    if (setting.maxCommissionAmount && parseFloat(setting.maxCommissionAmount) <= 0) {
      errors.maxCommissionAmount = 'Max commission amount must be positive';
    }

    return errors;
  };

  // Calculate estimated commission
  const calculateEstimatedCommission = (plan: SubscriptionPlan, setting: Partial<PackageCommissionSetting>) => {
    if (!setting.firstPurchaseCommission) return 0;

    const planPrice = parseFloat(plan.price);
    const commissionValue = parseFloat(setting.firstPurchaseCommission);

    if (setting.commissionType === 'percentage') {
      const calculated = (planPrice * commissionValue) / 100;
      const maxAmount = setting.maxCommissionAmount ? parseFloat(setting.maxCommissionAmount) : Infinity;
      return Math.min(calculated, maxAmount);
    } else {
      return commissionValue;
    }
  };

  const fetchData = async () => {
    try {
      const [settingsResponse, packageResponse, plansResponse] = await Promise.all([
        fetch('/api/admin/earning-world/referrals/commission-settings'),
        fetch('/api/admin/earning-world/referrals/package-commission-settings'),
        fetch('/api/admin/subscription-plans')
      ]);

      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        setSettings(settingsData.data);
      }

      if (packageResponse.ok) {
        const packageData = await packageResponse.json();
        setPackageSettings(packageData.data || []);
      }

      if (plansResponse.ok) {
        const plansData = await plansResponse.json();
        setSubscriptionPlans(plansData.data || []);
      }
    } catch (error) {
      console.error('Error fetching commission settings:', error);
      toast.error('Failed to load commission settings');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            if (settings) {
              handleSaveSettings();
            }
            break;
          case 'r':
            event.preventDefault();
            handleRefresh();
            break;
          case 'n':
            event.preventDefault();
            if (getAvailablePlans().length > 0) {
              handleAddNewPackage();
            }
            break;
          case 'h':
            event.preventDefault();
            setShowHelp(!showHelp);
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [settings, showHelp]);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchData();
    setAnalyticsRefreshTrigger(prev => prev + 1);
  };

  const handleSaveSettings = async () => {
    if (!settings) return;

    setSaving(true);
    try {
      const response = await fetch('/api/admin/earning-world/referrals/commission-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        toast.success('Commission settings saved successfully');
      } else {
        toast.error('Failed to save commission settings');
      }
    } catch (error) {
      console.error('Error saving commission settings:', error);
      toast.error('Failed to save commission settings');
    } finally {
      setSaving(false);
    }
  };

  const handleSavePackageSettings = async (packageSetting: PackageCommissionSetting) => {
    // Validate before saving
    const errors = validatePackageSetting(packageSetting);
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      toast.error('Please fix validation errors before saving');
      return;
    }

    try {
      const response = await fetch('/api/admin/earning-world/referrals/package-commission-settings', {
        method: packageSetting.id ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(packageSetting),
      });

      if (response.ok) {
        toast.success('Package commission settings saved successfully');
        fetchData();
        setShowAddPackage(false);
        setEditingPackage(null);
        setNewPackageSetting({
          planId: '',
          planName: '',
          isEnabled: true,
          commissionType: 'percentage',
          firstPurchaseCommission: '10.00',
          recurringCommission: '5.00',
          maxCommissionAmount: ''
        });
        setValidationErrors({});
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'Failed to save package commission settings');
      }
    } catch (error) {
      console.error('Error saving package commission settings:', error);
      toast.error('Failed to save package commission settings');
    }
  };

  const handleAddNewPackage = () => {
    setShowAddPackage(true);
    setEditingPackage(null);
    setValidationErrors({});
  };

  const handleEditPackage = (pkg: PackageCommissionSetting) => {
    setEditingPackage(pkg);
    setNewPackageSetting(pkg);
    setShowAddPackage(true);
    setValidationErrors({});
  };

  const handleCancelEdit = () => {
    setShowAddPackage(false);
    setEditingPackage(null);
    setNewPackageSetting({
      planId: '',
      planName: '',
      isEnabled: true,
      commissionType: 'percentage',
      firstPurchaseCommission: '10.00',
      recurringCommission: '5.00',
      maxCommissionAmount: ''
    });
    setValidationErrors({});
  };

  const handleDeletePackageSettings = async (id: string, planName: string) => {
    if (!confirm(`Are you sure you want to delete commission settings for "${planName}"? This action cannot be undone.`)) return;

    try {
      const response = await fetch(`/api/admin/earning-world/referrals/package-commission-settings/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success(`Commission settings for "${planName}" deleted successfully`);
        fetchData();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'Failed to delete package commission settings');
      }
    } catch (error) {
      console.error('Error deleting package commission settings:', error);
      toast.error('Failed to delete package commission settings');
    }
  };

  const handleBulkToggleStatus = async (ids: string[], enabled: boolean) => {
    try {
      const promises = ids.map(id =>
        fetch(`/api/admin/earning-world/referrals/package-commission-settings/${id}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ isEnabled: enabled })
        })
      );

      await Promise.all(promises);
      toast.success(`${ids.length} package settings ${enabled ? 'enabled' : 'disabled'} successfully`);
      fetchData();
    } catch (error) {
      console.error('Error updating package settings:', error);
      toast.error('Failed to update package settings');
    }
  };

  const getAvailablePlans = () => {
    const usedPlanIds = packageSettings.map(pkg => pkg.planId);
    return subscriptionPlans.filter(plan =>
      plan.isActive && !usedPlanIds.includes(plan.id)
    );
  };

  const handleExportSettings = () => {
    const exportData = {
      globalSettings: settings,
      packageSettings: packageSettings,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `commission-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Commission settings exported successfully');
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importData = JSON.parse(e.target?.result as string);

        if (!importData.globalSettings || !importData.packageSettings) {
          toast.error('Invalid import file format');
          return;
        }

        // You can add validation and confirmation dialog here
        if (confirm('This will overwrite current settings. Are you sure?')) {
          // Import logic would go here
          toast.success('Import functionality ready - implement based on requirements');
        }
      } catch (error) {
        toast.error('Failed to parse import file');
      }
    };
    reader.readAsText(file);

    // Reset input
    event.target.value = '';
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-64">
          <Spinner size="lg" />
          <p className="mt-4 text-gray-600">Loading commission settings...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <CurrencyDollarIcon className="h-8 w-8 mr-3 text-blue-600" />
              Commission Settings
            </h1>
            <p className="text-gray-600 mt-1">
              Configure referral commission rates and rules for subscription packages
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center"
              title="Refresh data (Ctrl+R)"
            >
              <ArrowPathIcon className={`h-5 w-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              onClick={() => setShowPreview(!showPreview)}
              variant="outline"
              className="flex items-center"
            >
              <ChartBarIcon className="h-5 w-5 mr-2" />
              {showPreview ? 'Hide' : 'Show'} Preview
            </Button>
            <Button
              onClick={() => setShowAnalytics(!showAnalytics)}
              variant="outline"
              className="flex items-center"
            >
              <ChartBarIcon className="h-5 w-5 mr-2" />
              {showAnalytics ? 'Hide' : 'Show'} Analytics
            </Button>
            <Button
              onClick={() => setShowValidator(!showValidator)}
              variant="outline"
              className="flex items-center"
            >
              <CheckIcon className="h-5 w-5 mr-2" />
              {showValidator ? 'Hide' : 'Show'} Validator
            </Button>
            <div className="flex space-x-2">
              <Button
                onClick={handleExportSettings}
                variant="outline"
                className="flex items-center"
              >
                <ArrowDownTrayIcon className="h-5 w-5 mr-2" />
                Export
              </Button>
              <div className="relative">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportSettings}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  id="import-settings"
                />
                <Button
                  variant="outline"
                  className="flex items-center"
                  as="label"
                  htmlFor="import-settings"
                >
                  <ArrowUpTrayIcon className="h-5 w-5 mr-2" />
                  Import
                </Button>
              </div>
            </div>
            <Button
              onClick={() => setShowHelp(!showHelp)}
              variant="outline"
              className="flex items-center"
              title="Show help documentation (Ctrl+H)"
            >
              <QuestionMarkCircleIcon className="h-5 w-5 mr-2" />
              Help
            </Button>
          </div>
        </div>

        {/* System Status Overview */}
        {settings && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className={`p-2 rounded-full ${settings.isEnabled ? 'bg-green-100' : 'bg-red-100'}`}>
                    {settings.isEnabled ? (
                      <CheckIcon className="h-6 w-6 text-green-600" />
                    ) : (
                      <XMarkIcon className="h-6 w-6 text-red-600" />
                    )}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">System Status</p>
                    <p className={`text-lg font-semibold ${settings.isEnabled ? 'text-green-600' : 'text-red-600'}`}>
                      {settings.isEnabled ? 'Active' : 'Disabled'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="p-2 rounded-full bg-green-100">
                    <BanknotesIcon className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">Default Rate</p>
                    <p className="text-lg font-semibold text-green-600">
                      {settings.defaultCommissionValue}{settings.defaultCommissionType === 'percentage' ? '%' : '$'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="p-2 rounded-full bg-purple-100">
                    <CalendarIcon className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">Eligibility Period</p>
                    <p className="text-lg font-semibold text-purple-600">
                      {settings.eligibilityPeriodDays} days
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="p-2 rounded-full bg-orange-100">
                    <ShieldCheckIcon className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-600">Package Settings</p>
                    <p className="text-lg font-semibold text-orange-600">
                      {packageSettings.length} configured
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Commission Analytics */}
        {showAnalytics && (
          <CommissionAnalytics refreshTrigger={analyticsRefreshTrigger} />
        )}

        {/* Commission Settings Validator */}
        {showValidator && settings && (
          <CommissionSettingsValidator
            settings={settings}
            packageSettings={packageSettings}
            subscriptionPlans={subscriptionPlans}
          />
        )}

        {/* Help Documentation */}
        {showHelp && (
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-transparent">
              <CardTitle className="flex items-center">
                <BookOpenIcon className="h-6 w-6 mr-2 text-blue-600" />
                Commission Settings Help
              </CardTitle>
              <CardDescription>
                Comprehensive guide to configuring referral commission settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Global Settings Help */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Global Commission Settings</h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div>
                    <strong>Enable Commission System:</strong> Master switch to turn the entire referral commission system on or off.
                  </div>
                  <div>
                    <strong>First Purchase Commission:</strong> Enable commissions for the first subscription purchase by referred users.
                  </div>
                  <div>
                    <strong>Recurring Purchase Commission:</strong> Enable commissions for subscription renewals by referred users.
                  </div>
                  <div>
                    <strong>Default Commission Type:</strong>
                    <ul className="ml-4 mt-1 space-y-1">
                      <li>• <strong>Percentage:</strong> Commission calculated as a percentage of the subscription price</li>
                      <li>• <strong>Fixed Amount:</strong> Commission is a fixed dollar amount regardless of subscription price</li>
                    </ul>
                  </div>
                  <div>
                    <strong>Max Commission Amount:</strong> Cap the maximum commission that can be earned per referral to prevent excessive payouts.
                  </div>
                  <div>
                    <strong>Eligibility Period:</strong> Number of days after registration during which a referred user's purchases count for commission.
                  </div>
                  <div>
                    <strong>Min Subscription Amount:</strong> Minimum subscription price required for commission eligibility.
                  </div>
                  <div>
                    <strong>Require Active Referrer:</strong> Only allow active users to earn commissions (prevents inactive account abuse).
                  </div>
                </div>
              </div>

              {/* Package Settings Help */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Package-Specific Settings</h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div>
                    <strong>Purpose:</strong> Override global commission rates for specific subscription plans to optimize earnings and incentives.
                  </div>
                  <div>
                    <strong>Use Cases:</strong>
                    <ul className="ml-4 mt-1 space-y-1">
                      <li>• Higher commissions for premium plans to encourage upselling</li>
                      <li>• Lower commissions for discounted plans to maintain profitability</li>
                      <li>• Fixed amounts for low-priced plans to ensure meaningful rewards</li>
                    </ul>
                  </div>
                  <div>
                    <strong>Best Practices:</strong>
                    <ul className="ml-4 mt-1 space-y-1">
                      <li>• Keep commission rates sustainable (typically 5-20% for percentage-based)</li>
                      <li>• Use max commission amounts to prevent excessive payouts on high-value plans</li>
                      <li>• Consider your profit margins when setting rates</li>
                      <li>• Test different rates to find optimal conversion vs. profitability balance</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Commission Calculation Examples */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Commission Calculation Examples</h4>
                <div className="space-y-3">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <strong className="text-sm">Example 1: Percentage Commission</strong>
                    <div className="text-sm text-gray-600 mt-1">
                      Plan Price: $29.99/month<br/>
                      Commission Rate: 10%<br/>
                      Commission Earned: $2.99
                    </div>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <strong className="text-sm">Example 2: Fixed Commission</strong>
                    <div className="text-sm text-gray-600 mt-1">
                      Plan Price: $9.99/month<br/>
                      Commission Rate: $2.00<br/>
                      Commission Earned: $2.00
                    </div>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <strong className="text-sm">Example 3: Capped Commission</strong>
                    <div className="text-sm text-gray-600 mt-1">
                      Plan Price: $199.99/year<br/>
                      Commission Rate: 15%<br/>
                      Calculated: $30.00<br/>
                      Max Commission: $25.00<br/>
                      Commission Earned: $25.00 (capped)
                    </div>
                  </div>
                </div>
              </div>

              {/* Troubleshooting */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Common Issues & Solutions</h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div>
                    <strong>Issue:</strong> Commissions not being calculated<br/>
                    <strong>Solution:</strong> Check that the commission system is enabled and the appropriate commission types are active.
                  </div>
                  <div>
                    <strong>Issue:</strong> Commission amounts seem incorrect<br/>
                    <strong>Solution:</strong> Verify package-specific settings aren't overriding global settings unexpectedly.
                  </div>
                  <div>
                    <strong>Issue:</strong> High commission costs<br/>
                    <strong>Solution:</strong> Implement max commission amounts and review percentage rates for sustainability.
                  </div>
                  <div>
                    <strong>Issue:</strong> Low referral conversion<br/>
                    <strong>Solution:</strong> Consider increasing commission rates or extending the eligibility period.
                  </div>
                </div>
              </div>

              {/* Keyboard Shortcuts */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">⌨️ Keyboard Shortcuts</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Save Settings:</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+S</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Refresh Data:</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+R</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Add Package:</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+N</kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>Toggle Help:</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl+H</kbd>
                  </div>
                </div>
              </div>

              {/* Tips */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">💡 Pro Tips</h4>
                <ul className="space-y-1 text-sm text-blue-800">
                  <li>• Use the validator tool to check for potential issues before saving</li>
                  <li>• Monitor analytics regularly to optimize commission rates</li>
                  <li>• Export settings before making major changes for backup</li>
                  <li>• Test commission calculations with the testing tool</li>
                  <li>• Consider seasonal adjustments for special promotions</li>
                  <li>• Use keyboard shortcuts for faster navigation and actions</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions Panel */}
        <Card className="bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200">
          <CardHeader>
            <CardTitle className="text-lg text-gray-800">Quick Actions</CardTitle>
            <CardDescription>Common commission management tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={() => {
                  if (settings) {
                    setSettings({ ...settings, isEnabled: !settings.isEnabled });
                    handleSaveSettings();
                  }
                }}
                variant="outline"
                className={`h-16 flex flex-col items-center justify-center space-y-1 ${
                  settings?.isEnabled
                    ? 'border-red-300 text-red-600 hover:bg-red-50'
                    : 'border-green-300 text-green-600 hover:bg-green-50'
                }`}
              >
                {settings?.isEnabled ? (
                  <>
                    <XMarkIcon className="h-6 w-6" />
                    <span className="text-sm">Disable System</span>
                  </>
                ) : (
                  <>
                    <CheckIcon className="h-6 w-6" />
                    <span className="text-sm">Enable System</span>
                  </>
                )}
              </Button>

              <Button
                onClick={() => {
                  const enabledCount = packageSettings.filter(pkg => pkg.isEnabled).length;
                  const disabledCount = packageSettings.length - enabledCount;

                  if (enabledCount > disabledCount) {
                    // Disable all
                    handleBulkToggleStatus(packageSettings.map(pkg => pkg.id), false);
                  } else {
                    // Enable all
                    handleBulkToggleStatus(packageSettings.map(pkg => pkg.id), true);
                  }
                }}
                variant="outline"
                className="h-16 flex flex-col items-center justify-center space-y-1 border-blue-300 text-blue-600 hover:bg-blue-50"
                disabled={packageSettings.length === 0}
              >
                <ArrowPathIcon className="h-6 w-6" />
                <span className="text-sm">Toggle All Packages</span>
              </Button>

              <Button
                onClick={() => {
                  setShowAnalytics(true);
                  setAnalyticsRefreshTrigger(prev => prev + 1);
                }}
                variant="outline"
                className="h-16 flex flex-col items-center justify-center space-y-1 border-purple-300 text-purple-600 hover:bg-purple-50"
              >
                <ChartBarIcon className="h-6 w-6" />
                <span className="text-sm">View Analytics</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Global Commission Settings */}
        {settings && (
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-transparent">
              <CardTitle className="flex items-center">
                <Cog6ToothIcon className="h-6 w-6 mr-2 text-blue-600" />
                Global Commission Settings
              </CardTitle>
              <CardDescription className="flex items-center">
                <InformationCircleIcon className="h-4 w-4 mr-1 text-blue-500" />
                Configure default commission rates and system behavior. These settings apply to all packages unless overridden.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* System Enable/Disable */}
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="system-enabled">Enable Commission System</Label>
                  <p className="text-sm text-gray-500">
                    Turn on/off the entire referral commission system
                  </p>
                </div>
                <Switch
                  id="system-enabled"
                  checked={settings.isEnabled}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, isEnabled: checked })
                  }
                />
              </div>

              {/* Commission Types */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="first-purchase">First Purchase Commission</Label>
                    <p className="text-sm text-gray-500">
                      Commission for first subscription purchase
                    </p>
                  </div>
                  <Switch
                    id="first-purchase"
                    checked={settings.firstPurchaseEnabled}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, firstPurchaseEnabled: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="recurring-purchase">Recurring Purchase Commission</Label>
                    <p className="text-sm text-gray-500">
                      Commission for subscription renewals
                    </p>
                  </div>
                  <Switch
                    id="recurring-purchase"
                    checked={settings.recurringPurchaseEnabled}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, recurringPurchaseEnabled: checked })
                    }
                  />
                </div>
              </div>

              {/* Default Commission Rates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="commission-type">Default Commission Type</Label>
                  <select
                    id="commission-type"
                    value={settings.defaultCommissionType}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        defaultCommissionType: e.target.value as 'fixed' | 'percentage'
                      })
                    }
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="percentage">Percentage</option>
                    <option value="fixed">Fixed Amount</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="commission-value">
                    Default Commission Value ({settings.defaultCommissionType === 'percentage' ? '%' : '$'})
                  </Label>
                  <Input
                    id="commission-value"
                    type="number"
                    step="0.01"
                    value={settings.defaultCommissionValue}
                    onChange={(e) =>
                      setSettings({ ...settings, defaultCommissionValue: e.target.value })
                    }
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Recurring Commission Value */}
              {settings.recurringPurchaseEnabled && (
                <div>
                  <Label htmlFor="recurring-commission-value">
                    Recurring Commission Value ({settings.defaultCommissionType === 'percentage' ? '%' : '$'})
                  </Label>
                  <Input
                    id="recurring-commission-value"
                    type="number"
                    step="0.01"
                    value={settings.defaultRecurringCommissionValue}
                    onChange={(e) =>
                      setSettings({ ...settings, defaultRecurringCommissionValue: e.target.value })
                    }
                    className="mt-1"
                  />
                </div>
              )}

              {/* Limits and Eligibility */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="max-commission">Max Commission Amount ($)</Label>
                  <Input
                    id="max-commission"
                    type="number"
                    step="0.01"
                    value={settings.maxCommissionAmount}
                    onChange={(e) =>
                      setSettings({ ...settings, maxCommissionAmount: e.target.value })
                    }
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="eligibility-period">Eligibility Period (Days)</Label>
                  <Input
                    id="eligibility-period"
                    type="number"
                    value={settings.eligibilityPeriodDays}
                    onChange={(e) =>
                      setSettings({ ...settings, eligibilityPeriodDays: parseInt(e.target.value) || 0 })
                    }
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="min-subscription">Min Subscription Amount ($)</Label>
                  <Input
                    id="min-subscription"
                    type="number"
                    step="0.01"
                    value={settings.minSubscriptionAmount}
                    onChange={(e) =>
                      setSettings({ ...settings, minSubscriptionAmount: e.target.value })
                    }
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Additional Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="require-active">Require Active Referrer</Label>
                    <p className="text-sm text-gray-500">
                      Only active referrers can earn commissions
                    </p>
                  </div>
                  <Switch
                    id="require-active"
                    checked={settings.requireActiveReferrer}
                    onCheckedChange={(checked) =>
                      setSettings({ ...settings, requireActiveReferrer: checked })
                    }
                  />
                </div>

                <div>
                  <Label htmlFor="max-commissions">Max Commissions per Referral (0 = unlimited)</Label>
                  <Input
                    id="max-commissions"
                    type="number"
                    value={settings.maxCommissionsPerReferral}
                    onChange={(e) =>
                      setSettings({ ...settings, maxCommissionsPerReferral: parseInt(e.target.value) || 0 })
                    }
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button
                  onClick={handleSaveSettings}
                  disabled={saving}
                  className="bg-blue-600 hover:bg-blue-700"
                  title="Save global settings (Ctrl+S)"
                >
                  {saving ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Saving...
                    </>
                  ) : (
                    'Save Settings'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Package-Specific Commission Settings */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  <CurrencyDollarIcon className="h-6 w-6 mr-2" />
                  Package-Specific Commission Settings
                </CardTitle>
                <CardDescription>
                  Override default commission rates for specific subscription packages
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={handleAddNewPackage}
                  className="bg-green-600 hover:bg-green-700"
                  disabled={getAvailablePlans().length === 0}
                  title="Add new package setting (Ctrl+N)"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Package Setting
                </Button>
                {packageSettings.length > 0 && (
                  <Button
                    onClick={() => handleBulkToggleStatus(
                      packageSettings.filter(pkg => !pkg.isEnabled).map(pkg => pkg.id),
                      true
                    )}
                    variant="outline"
                    className="text-green-600 border-green-600 hover:bg-green-50"
                  >
                    <CheckIcon className="h-4 w-4 mr-2" />
                    Enable All
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {packageSettings.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Package Details
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Commission Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        First Purchase
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Recurring
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Max Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Estimated Earnings
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {packageSettings.map((pkg) => {
                      const plan = subscriptionPlans.find(p => p.id === pkg.planId);
                      const estimatedEarning = plan ? calculateEstimatedCommission(plan, pkg) : 0;

                      return (
                        <tr key={pkg.id} className="hover:bg-gray-50 transition-colors">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{pkg.planName}</div>
                                {plan && (
                                  <div className="text-xs text-gray-500">
                                    ${plan.price}/{plan.billingCycle} • {plan.currency}
                                  </div>
                                )}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              pkg.commissionType === 'percentage'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-purple-100 text-purple-800'
                            }`}>
                              {pkg.commissionType === 'percentage' ? 'Percentage' : 'Fixed Amount'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {pkg.firstPurchaseCommission}{pkg.commissionType === 'percentage' ? '%' : '$'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {pkg.recurringCommission}{pkg.commissionType === 'percentage' ? '%' : '$'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {pkg.maxCommissionAmount ? `$${pkg.maxCommissionAmount}` : (
                                <span className="text-gray-400 italic">No limit</span>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-green-600">
                              ${estimatedEarning.toFixed(2)}
                            </div>
                            <div className="text-xs text-gray-500">per referral</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              pkg.isEnabled
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {pkg.isEnabled ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditPackage(pkg)}
                                className="text-blue-600 border-blue-600 hover:bg-blue-50"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDeletePackageSettings(pkg.id, pkg.planName)}
                                className="text-red-600 border-red-600 hover:bg-red-50"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="bg-gray-100 rounded-full p-6 w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                  <CurrencyDollarIcon className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Package-Specific Settings
                </h3>
                <p className="text-gray-500 mb-4 max-w-md mx-auto">
                  Create custom commission rates for specific subscription packages to override the default global settings.
                </p>
                {getAvailablePlans().length > 0 ? (
                  <Button
                    onClick={handleAddNewPackage}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Your First Package Setting
                  </Button>
                ) : (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto">
                    <div className="flex items-center">
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2" />
                      <p className="text-sm text-yellow-800">
                        All active subscription plans already have commission settings configured.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Add/Edit Package Modal */}
        {showAddPackage && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <CurrencyDollarIcon className="h-6 w-6 mr-2 text-blue-600" />
                    {editingPackage ? 'Edit Package Commission' : 'Add Package Commission'}
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancelEdit}
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="p-6 space-y-6">
                {/* Plan Selection */}
                <div>
                  <Label htmlFor="plan-select">Subscription Plan *</Label>
                  <select
                    id="plan-select"
                    value={newPackageSetting.planId || ''}
                    onChange={(e) => {
                      const selectedPlan = subscriptionPlans.find(p => p.id === e.target.value);
                      setNewPackageSetting({
                        ...newPackageSetting,
                        planId: e.target.value,
                        planName: selectedPlan?.displayName || ''
                      });
                      setValidationErrors({ ...validationErrors, planId: '' });
                    }}
                    className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                      validationErrors.planId ? 'border-red-300' : ''
                    }`}
                    disabled={!!editingPackage}
                  >
                    <option value="">Select a subscription plan</option>
                    {(editingPackage ? subscriptionPlans : getAvailablePlans()).map((plan) => (
                      <option key={plan.id} value={plan.id}>
                        {plan.displayName} - ${plan.price}/{plan.billingCycle}
                      </option>
                    ))}
                  </select>
                  {validationErrors.planId && (
                    <p className="mt-1 text-sm text-red-600">{validationErrors.planId}</p>
                  )}
                </div>

                {/* Commission Type */}
                <div>
                  <Label htmlFor="commission-type-modal">Commission Type</Label>
                  <select
                    id="commission-type-modal"
                    value={newPackageSetting.commissionType || 'percentage'}
                    onChange={(e) =>
                      setNewPackageSetting({
                        ...newPackageSetting,
                        commissionType: e.target.value as 'fixed' | 'percentage'
                      })
                    }
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  >
                    <option value="percentage">Percentage (%)</option>
                    <option value="fixed">Fixed Amount ($)</option>
                  </select>
                </div>

                {/* Commission Values */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="first-purchase-commission">
                      First Purchase Commission ({newPackageSetting.commissionType === 'percentage' ? '%' : '$'}) *
                    </Label>
                    <Input
                      id="first-purchase-commission"
                      type="number"
                      step="0.01"
                      min="0"
                      max={newPackageSetting.commissionType === 'percentage' ? '100' : undefined}
                      value={newPackageSetting.firstPurchaseCommission || ''}
                      onChange={(e) => {
                        setNewPackageSetting({
                          ...newPackageSetting,
                          firstPurchaseCommission: e.target.value
                        });
                        setValidationErrors({ ...validationErrors, firstPurchaseCommission: '' });
                      }}
                      className={`mt-1 ${validationErrors.firstPurchaseCommission ? 'border-red-300' : ''}`}
                      placeholder={newPackageSetting.commissionType === 'percentage' ? '10.00' : '5.00'}
                    />
                    {validationErrors.firstPurchaseCommission && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.firstPurchaseCommission}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="recurring-commission">
                      Recurring Commission ({newPackageSetting.commissionType === 'percentage' ? '%' : '$'})
                    </Label>
                    <Input
                      id="recurring-commission"
                      type="number"
                      step="0.01"
                      min="0"
                      max={newPackageSetting.commissionType === 'percentage' ? '100' : undefined}
                      value={newPackageSetting.recurringCommission || ''}
                      onChange={(e) => {
                        setNewPackageSetting({
                          ...newPackageSetting,
                          recurringCommission: e.target.value
                        });
                        setValidationErrors({ ...validationErrors, recurringCommission: '' });
                      }}
                      className={`mt-1 ${validationErrors.recurringCommission ? 'border-red-300' : ''}`}
                      placeholder={newPackageSetting.commissionType === 'percentage' ? '5.00' : '2.50'}
                    />
                    {validationErrors.recurringCommission && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.recurringCommission}</p>
                    )}
                  </div>
                </div>

                {/* Max Commission Amount */}
                <div>
                  <Label htmlFor="max-commission-amount">Maximum Commission Amount ($)</Label>
                  <Input
                    id="max-commission-amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={newPackageSetting.maxCommissionAmount || ''}
                    onChange={(e) => {
                      setNewPackageSetting({
                        ...newPackageSetting,
                        maxCommissionAmount: e.target.value
                      });
                      setValidationErrors({ ...validationErrors, maxCommissionAmount: '' });
                    }}
                    className={`mt-1 ${validationErrors.maxCommissionAmount ? 'border-red-300' : ''}`}
                    placeholder="Leave empty for no limit"
                  />
                  {validationErrors.maxCommissionAmount && (
                    <p className="mt-1 text-sm text-red-600">{validationErrors.maxCommissionAmount}</p>
                  )}
                  <p className="mt-1 text-sm text-gray-500">
                    Optional: Set a maximum commission amount to cap earnings per referral
                  </p>
                </div>

                {/* Enable/Disable */}
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="package-enabled">Enable Commission</Label>
                    <p className="text-sm text-gray-500">
                      Enable or disable commission for this package
                    </p>
                  </div>
                  <Switch
                    id="package-enabled"
                    checked={newPackageSetting.isEnabled || false}
                    onCheckedChange={(checked) =>
                      setNewPackageSetting({ ...newPackageSetting, isEnabled: checked })
                    }
                  />
                </div>

                {/* Preview */}
                {newPackageSetting.planId && newPackageSetting.firstPurchaseCommission && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-blue-900 mb-2 flex items-center">
                      <InformationCircleIcon className="h-4 w-4 mr-1" />
                      Commission Preview
                    </h4>
                    {(() => {
                      const selectedPlan = subscriptionPlans.find(p => p.id === newPackageSetting.planId);
                      if (!selectedPlan) return null;

                      const estimatedEarning = calculateEstimatedCommission(selectedPlan, newPackageSetting);

                      return (
                        <div className="text-sm text-blue-800">
                          <p>Plan: <span className="font-medium">{selectedPlan.displayName}</span></p>
                          <p>Plan Price: <span className="font-medium">${selectedPlan.price}/{selectedPlan.billingCycle}</span></p>
                          <p>Estimated Earning: <span className="font-medium text-green-600">${estimatedEarning.toFixed(2)} per referral</span></p>
                        </div>
                      );
                    })()}
                  </div>
                )}
              </div>

              <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={handleCancelEdit}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => handleSavePackageSettings({
                    id: editingPackage?.id || '',
                    planId: newPackageSetting.planId || '',
                    planName: newPackageSetting.planName || '',
                    isEnabled: newPackageSetting.isEnabled || false,
                    commissionType: newPackageSetting.commissionType || 'percentage',
                    firstPurchaseCommission: newPackageSetting.firstPurchaseCommission || '',
                    recurringCommission: newPackageSetting.recurringCommission || '',
                    maxCommissionAmount: newPackageSetting.maxCommissionAmount
                  })}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {editingPackage ? 'Update Settings' : 'Create Settings'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
