"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogPanel, DialogTitle } from "@headlessui/react";
import { XMarkIcon, HeartIcon, HandThumbDownIcon } from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid, HandThumbDownIcon as HandThumbDownIconSolid } from "@heroicons/react/24/solid";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Spinner } from "@/components/ui/Spinner";
import Link from "next/link";

interface ReactionUser {
  id: string;
  name: string;
  username?: string | null;
  image: string | null;
  reactionType: 'like' | 'dislike';
  createdAt: string;
}

interface PostReactionsProps {
  isOpen: boolean;
  onClose: () => void;
  postId: string;
  postType?: 'user_post' | 'fan_page_post' | 'blog_post';
  blogSlug?: string;
  initialTab?: 'likes' | 'dislikes';
}

export function PostReactions({ 
  isOpen, 
  onClose, 
  postId, 
  postType = 'user_post',
  blogSlug,
  initialTab = 'likes' 
}: PostReactionsProps) {
  const [activeTab, setActiveTab] = useState<'likes' | 'dislikes'>(initialTab);
  const [likes, setLikes] = useState<ReactionUser[]>([]);
  const [dislikes, setDislikes] = useState<ReactionUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch reactions data
  const fetchReactions = async () => {
    if (!isOpen) return;
    
    setLoading(true);
    setError(null);

    try {
      // Determine API endpoint based on post type
      let apiUrl: string;
      if (postType === 'blog_post' && blogSlug) {
        apiUrl = `/api/blogs/${blogSlug}/reactions`;
      } else if (postType === 'fan_page_post') {
        apiUrl = `/api/fan-pages/posts/${postId}/reactions`;
      } else {
        apiUrl = `/api/posts/${postId}/reactions`;
      }

      const response = await fetch(apiUrl);
      if (!response.ok) {
        throw new Error('Failed to fetch reactions');
      }

      const data = await response.json();
      setLikes(data.likes || []);
      setDislikes(data.dislikes || []);
    } catch (err) {
      console.error('Error fetching reactions:', err);
      setError('Failed to load reactions');
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when modal opens
  useEffect(() => {
    fetchReactions();
  }, [isOpen, postId, postType, blogSlug]);

  // Reset tab when modal opens
  useEffect(() => {
    if (isOpen) {
      setActiveTab(initialTab);
    }
  }, [isOpen, initialTab]);

  const renderUserList = (users: ReactionUser[]) => {
    if (users.length === 0) {
      return (
        <div className="text-center py-12 text-gray-500">
          <div className="mb-3">
            {activeTab === 'likes' ? (
              <HeartIcon className="h-12 w-12 mx-auto text-gray-300" />
            ) : (
              <HandThumbDownIcon className="h-12 w-12 mx-auto text-gray-300" />
            )}
          </div>
          <p className="text-sm font-medium">No {activeTab} yet</p>
          <p className="text-xs text-gray-400 mt-1">
            Be the first to {activeTab === 'likes' ? 'like' : 'dislike'} this post!
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {users.map((user) => (
          <div key={`${user.id}-${user.reactionType}`} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors duration-200 border border-transparent hover:border-gray-200">
            {/* User Avatar */}
            <div className="flex-shrink-0">
              <div className="relative h-10 w-10 rounded-full overflow-hidden bg-gray-200">
                {user.image ? (
                  <OptimizedImage
                    src={user.image}
                    alt={user.name}
                    width={40}
                    height={40}
                    className="rounded-full object-cover"
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-bold">
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
            </div>

            {/* User Info */}
            <div className="flex-1 min-w-0">
              <Link
                href={`/user/${user.username || user.id}`}
                className="block hover:underline"
                onClick={(e) => e.stopPropagation()}
              >
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user.name}
                </p>
                {user.username && (
                  <p className="text-xs text-gray-500 truncate">
                    @{user.username}
                  </p>
                )}
              </Link>
            </div>

            {/* Reaction Icon */}
            <div className="flex-shrink-0">
              {user.reactionType === 'like' ? (
                <HeartIconSolid className="h-5 w-5 text-red-500" />
              ) : (
                <HandThumbDownIconSolid className="h-5 w-5 text-gray-500" />
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-md w-full bg-white rounded-xl shadow-xl border border-gray-200">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <DialogTitle className="text-lg font-semibold text-gray-900">
              Reactions
            </DialogTitle>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-gray-200">
            <button
              onClick={() => setActiveTab('likes')}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'likes'
                  ? 'text-red-600 border-b-2 border-red-600 bg-red-50'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <HeartIcon className="h-4 w-4" />
                <span>Likes ({likes.length})</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('dislikes')}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'dislikes'
                  ? 'text-gray-600 border-b-2 border-gray-600 bg-gray-50'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <HandThumbDownIcon className="h-4 w-4" />
                <span>Dislikes ({dislikes.length})</span>
              </div>
            </button>
          </div>

          {/* Content */}
          <div className="p-4 max-h-96 overflow-y-auto">
            {loading ? (
              <div className="flex justify-center py-8">
                <Spinner size="md" />
              </div>
            ) : error ? (
              <div className="text-center py-8 text-red-500">
                <p>{error}</p>
                <button
                  onClick={fetchReactions}
                  className="mt-2 text-sm text-blue-600 hover:underline"
                >
                  Try again
                </button>
              </div>
            ) : (
              renderUserList(activeTab === 'likes' ? likes : dislikes)
            )}
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
