import mysql from "mysql2/promise";
import * as dotenv from "dotenv";
import * as fs from "fs";
import * as path from "path";

dotenv.config();

async function main() {
  console.log("Setting up database for cPanel MySQL...");

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  try {
    // Connect to the database
    const connection = await mysql.createConnection({
      host: dbHost,
      user: dbUser,
      password: dbPassword,
      database: dbName,
      port: dbPort,
      multipleStatements: true,
    });

    console.log(`Connected to database '${dbName}'`);
    console.log("Creating tables...");

    // SQL script to create all tables
    const sql = `
    -- Users table
    CREATE TABLE IF NOT EXISTS users (
      id VARCHAR(255) PRIMARY KEY,
      name VARCHAR(255),
      username VARCHAR(50) UNIQUE,
      email VARCHAR(255) NOT NULL UNIQUE,
      emailVerified TIMESTAMP NULL,
      image VARCHAR(255),
      coverImage VARCHAR(255),
      bio TEXT,
      location VARCHAR(255),
      birthday DATETIME,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );

    -- Accounts table
    CREATE TABLE IF NOT EXISTS accounts (
      id VARCHAR(255) PRIMARY KEY,
      userId VARCHAR(255) NOT NULL,
      type VARCHAR(255) NOT NULL,
      provider VARCHAR(255) NOT NULL,
      providerAccountId VARCHAR(255) NOT NULL,
      refresh_token TEXT,
      access_token TEXT,
      expires_at INT,
      token_type VARCHAR(255),
      scope VARCHAR(255),
      id_token TEXT,
      session_state VARCHAR(255),
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
    );

    -- Sessions table
    CREATE TABLE IF NOT EXISTS sessions (
      id VARCHAR(255) PRIMARY KEY,
      sessionToken VARCHAR(255) NOT NULL UNIQUE,
      userId VARCHAR(255) NOT NULL,
      expires TIMESTAMP NOT NULL,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
    );

    -- Verification tokens table
    CREATE TABLE IF NOT EXISTS verificationTokens (
      identifier VARCHAR(255) NOT NULL,
      token VARCHAR(255) NOT NULL,
      expires TIMESTAMP NOT NULL,
      PRIMARY KEY (identifier, token)
    );

    -- Posts table
    CREATE TABLE IF NOT EXISTS posts (
      id VARCHAR(255) PRIMARY KEY,
      userId VARCHAR(255) NOT NULL,
      content TEXT,
      images JSON,
      videos JSON,
      privacy ENUM('public', 'friends', 'private') NOT NULL DEFAULT 'public',
      sharedPostId VARCHAR(255),
      backgroundColor VARCHAR(50),
      feeling VARCHAR(100),
      activity VARCHAR(100),
      location VARCHAR(255),
      formattedContent BOOLEAN DEFAULT FALSE,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (sharedPostId) REFERENCES posts(id) ON DELETE SET NULL
    );

    -- Comments table
    CREATE TABLE IF NOT EXISTS comments (
      id VARCHAR(255) PRIMARY KEY,
      content TEXT NOT NULL,
      userId VARCHAR(255) NOT NULL,
      postId VARCHAR(255) NOT NULL,
      parentId VARCHAR(255),
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (postId) REFERENCES posts(id) ON DELETE CASCADE,
      FOREIGN KEY (parentId) REFERENCES comments(id) ON DELETE SET NULL
    );

    -- Likes table
    CREATE TABLE IF NOT EXISTS likes (
      id VARCHAR(255) PRIMARY KEY,
      userId VARCHAR(255) NOT NULL,
      postId VARCHAR(255),
      commentId VARCHAR(255),
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (postId) REFERENCES posts(id) ON DELETE CASCADE,
      FOREIGN KEY (commentId) REFERENCES comments(id) ON DELETE CASCADE
    );

    -- Friendships table
    CREATE TABLE IF NOT EXISTS friendships (
      id VARCHAR(255) PRIMARY KEY,
      user1Id VARCHAR(255) NOT NULL,
      user2Id VARCHAR(255) NOT NULL,
      status ENUM('pending', 'accepted', 'rejected', 'blocked') NOT NULL DEFAULT 'pending',
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (user1Id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (user2Id) REFERENCES users(id) ON DELETE CASCADE
    );

    -- Messages table
    CREATE TABLE IF NOT EXISTS messages (
      id VARCHAR(255) PRIMARY KEY,
      senderId VARCHAR(255) NOT NULL,
      receiverId VARCHAR(255) NOT NULL,
      content TEXT NOT NULL,
      \`read\` BOOLEAN NOT NULL DEFAULT FALSE,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (senderId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (receiverId) REFERENCES users(id) ON DELETE CASCADE
    );

    -- Notifications table
    CREATE TABLE IF NOT EXISTS notifications (
      id VARCHAR(255) PRIMARY KEY,
      recipientId VARCHAR(255) NOT NULL,
      type ENUM('like', 'comment', 'friend_request', 'friend_accept', 'message') NOT NULL,
      senderId VARCHAR(255),
      postId VARCHAR(255),
      commentId VARCHAR(255),
      messageId VARCHAR(255),
      friendshipId VARCHAR(255),
      \`read\` BOOLEAN NOT NULL DEFAULT FALSE,
      createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (recipientId) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (senderId) REFERENCES users(id) ON DELETE SET NULL,
      FOREIGN KEY (postId) REFERENCES posts(id) ON DELETE SET NULL,
      FOREIGN KEY (commentId) REFERENCES comments(id) ON DELETE SET NULL,
      FOREIGN KEY (messageId) REFERENCES messages(id) ON DELETE SET NULL,
      FOREIGN KEY (friendshipId) REFERENCES friendships(id) ON DELETE SET NULL
    );
    `;

    // Execute the SQL script
    await connection.query(sql);

    console.log("Database setup complete!");
    console.log("Tables created successfully.");

    // Close the connection
    await connection.end();
  } catch (error) {
    console.error("Error setting up database:", error);
    process.exit(1);
  }
}

main();
