import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log('Starting migration to fix collation issues between users and group_members tables...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
  });

  try {
    // Check current collations
    console.log("Checking current collations...");
    const [usersCollation] = await connection.query(
      `SELECT table_name, table_collation
       FROM information_schema.TABLES
       WHERE table_schema = ? AND table_name = 'users'`,
      [dbName]
    );
    console.log("Users table collation:", usersCollation);

    const [groupMembersCollation] = await connection.query(
      `SELECT table_name, table_collation
       FROM information_schema.TABLES
       WHERE table_schema = ? AND table_name = 'group_members'`,
      [dbName]
    );
    console.log("Group members table collation:", groupMembersCollation);

    // Check if there are foreign keys on the group_members table
    console.log("Checking for foreign keys on group_members table...");
    const [fks] = await connection.query(`
      SELECT CONSTRAINT_NAME
      FROM information_schema.TABLE_CONSTRAINTS
      WHERE TABLE_NAME = 'group_members'
      AND CONSTRAINT_TYPE = 'FOREIGN KEY'
      AND TABLE_SCHEMA = ?
    `, [dbName]);

    // Drop foreign keys if they exist
    if (Array.isArray(fks) && fks.length > 0) {
      console.log("Dropping foreign keys on group_members table...");
      for (const fk of fks) {
        // @ts-ignore
        await connection.query(`ALTER TABLE group_members DROP FOREIGN KEY ${fk.CONSTRAINT_NAME}`);
        // @ts-ignore
        console.log(`Dropped foreign key: ${fk.CONSTRAINT_NAME}`);
      }
    } else {
      console.log("No foreign keys found on group_members table.");
    }

    // Update the userId column in group_members table to match the collation of users.id
    console.log("Updating userId column in group_members table...");
    await connection.query(`
      ALTER TABLE group_members 
      MODIFY COLUMN userId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci
    `);
    console.log("Successfully updated userId column in group_members table.");

    // Re-add foreign key if needed
    console.log("Re-adding foreign key constraint...");
    try {
      await connection.query(`
        ALTER TABLE group_members
        ADD CONSTRAINT fk_group_members_user
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
      `);
      console.log("Successfully re-added foreign key constraint.");
    } catch (error) {
      console.error("Error re-adding foreign key constraint:", error);
    }

    console.log("Collation update completed successfully!");
  } catch (error) {
    console.error("Error during migration:", error);
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
