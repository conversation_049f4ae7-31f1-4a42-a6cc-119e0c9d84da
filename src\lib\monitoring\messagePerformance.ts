/**
 * WhatsApp-Style Message Performance Monitoring
 * 
 * Tracks:
 * - Message delivery times
 * - Connection quality
 * - User experience metrics
 * - Cache performance
 * - Real-time latency
 */

interface PerformanceMetric {
  id: string;
  type: 'message_send' | 'message_receive' | 'connection' | 'cache' | 'ui_response';
  timestamp: number;
  duration?: number;
  success: boolean;
  metadata?: Record<string, any>;
}

interface ConnectionQuality {
  latency: number;
  packetLoss: number;
  bandwidth: number;
  stability: 'excellent' | 'good' | 'fair' | 'poor';
  lastUpdated: number;
}

interface MessageDeliveryStats {
  averageDeliveryTime: number;
  successRate: number;
  retryRate: number;
  optimisticAccuracy: number;
  totalMessages: number;
}

interface CachePerformanceStats {
  hitRate: number;
  missRate: number;
  averageResponseTime: number;
  memoryUsage: number;
  totalRequests: number;
}

interface UIPerformanceStats {
  averageRenderTime: number;
  scrollPerformance: number;
  inputLatency: number;
  animationFrameRate: number;
}

class MessagePerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private connectionQuality: ConnectionQuality = {
    latency: 0,
    packetLoss: 0,
    bandwidth: 0,
    stability: 'good',
    lastUpdated: Date.now(),
  };
  
  private maxMetrics = 1000; // Keep last 1000 metrics
  private performanceObserver?: PerformanceObserver;
  private connectionTestInterval?: NodeJS.Timeout;

  constructor() {
    this.initializePerformanceObserver();
    this.startConnectionMonitoring();
  }

  /**
   * Track message sending performance
   */
  trackMessageSend(messageId: string, startTime: number): void {
    const duration = Date.now() - startTime;
    this.addMetric({
      id: messageId,
      type: 'message_send',
      timestamp: Date.now(),
      duration,
      success: true,
      metadata: { messageId },
    });
  }

  /**
   * Track message sending failure
   */
  trackMessageSendFailure(messageId: string, startTime: number, error: string): void {
    const duration = Date.now() - startTime;
    this.addMetric({
      id: messageId,
      type: 'message_send',
      timestamp: Date.now(),
      duration,
      success: false,
      metadata: { messageId, error },
    });
  }

  /**
   * Track message receiving performance
   */
  trackMessageReceive(messageId: string, sentTime: number): void {
    const duration = Date.now() - sentTime;
    this.addMetric({
      id: messageId,
      type: 'message_receive',
      timestamp: Date.now(),
      duration,
      success: true,
      metadata: { messageId },
    });
  }

  /**
   * Track connection quality
   */
  updateConnectionQuality(latency: number, packetLoss: number = 0): void {
    this.connectionQuality = {
      latency,
      packetLoss,
      bandwidth: this.estimateBandwidth(latency),
      stability: this.calculateStability(latency, packetLoss),
      lastUpdated: Date.now(),
    };

    this.addMetric({
      id: `connection_${Date.now()}`,
      type: 'connection',
      timestamp: Date.now(),
      success: latency < 1000, // Consider good if under 1 second
      metadata: { ...this.connectionQuality },
    });
  }

  /**
   * Track cache performance
   */
  trackCacheHit(key: string, responseTime: number): void {
    this.addMetric({
      id: `cache_hit_${Date.now()}`,
      type: 'cache',
      timestamp: Date.now(),
      duration: responseTime,
      success: true,
      metadata: { key, type: 'hit' },
    });
  }

  trackCacheMiss(key: string, responseTime: number): void {
    this.addMetric({
      id: `cache_miss_${Date.now()}`,
      type: 'cache',
      timestamp: Date.now(),
      duration: responseTime,
      success: false,
      metadata: { key, type: 'miss' },
    });
  }

  /**
   * Track UI response times
   */
  trackUIResponse(action: string, duration: number): void {
    this.addMetric({
      id: `ui_${action}_${Date.now()}`,
      type: 'ui_response',
      timestamp: Date.now(),
      duration,
      success: duration < 100, // Consider good if under 100ms
      metadata: { action },
    });
  }

  /**
   * Get message delivery statistics
   */
  getMessageDeliveryStats(): MessageDeliveryStats {
    const messageMetrics = this.metrics.filter(m => m.type === 'message_send');
    const successfulMessages = messageMetrics.filter(m => m.success);
    const retriedMessages = messageMetrics.filter(m => m.metadata?.retryCount > 0);

    return {
      averageDeliveryTime: this.calculateAverage(successfulMessages.map(m => m.duration || 0)),
      successRate: messageMetrics.length > 0 ? successfulMessages.length / messageMetrics.length : 0,
      retryRate: messageMetrics.length > 0 ? retriedMessages.length / messageMetrics.length : 0,
      optimisticAccuracy: this.calculateOptimisticAccuracy(),
      totalMessages: messageMetrics.length,
    };
  }

  /**
   * Get cache performance statistics
   */
  getCachePerformanceStats(): CachePerformanceStats {
    const cacheMetrics = this.metrics.filter(m => m.type === 'cache');
    const hits = cacheMetrics.filter(m => m.success);
    const misses = cacheMetrics.filter(m => !m.success);

    return {
      hitRate: cacheMetrics.length > 0 ? hits.length / cacheMetrics.length : 0,
      missRate: cacheMetrics.length > 0 ? misses.length / cacheMetrics.length : 0,
      averageResponseTime: this.calculateAverage(cacheMetrics.map(m => m.duration || 0)),
      memoryUsage: this.estimateMemoryUsage(),
      totalRequests: cacheMetrics.length,
    };
  }

  /**
   * Get UI performance statistics
   */
  getUIPerformanceStats(): UIPerformanceStats {
    const uiMetrics = this.metrics.filter(m => m.type === 'ui_response');
    
    return {
      averageRenderTime: this.calculateAverage(uiMetrics.map(m => m.duration || 0)),
      scrollPerformance: this.calculateScrollPerformance(),
      inputLatency: this.calculateInputLatency(),
      animationFrameRate: this.calculateFrameRate(),
    };
  }

  /**
   * Get current connection quality
   */
  getConnectionQuality(): ConnectionQuality {
    return { ...this.connectionQuality };
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    return {
      messageDelivery: this.getMessageDeliveryStats(),
      cachePerformance: this.getCachePerformanceStats(),
      uiPerformance: this.getUIPerformanceStats(),
      connectionQuality: this.getConnectionQuality(),
      overallHealth: this.calculateOverallHealth(),
    };
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  /**
   * Clear old metrics
   */
  clearOldMetrics(): void {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
  }

  // Private methods

  private addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  private initializePerformanceObserver(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation' || entry.entryType === 'measure') {
            this.trackUIResponse(entry.name, entry.duration);
          }
        });
      });

      this.performanceObserver.observe({ entryTypes: ['navigation', 'measure'] });
    } catch (error) {
      console.warn('Performance Observer not supported:', error);
    }
  }

  private startConnectionMonitoring(): void {
    if (typeof window === 'undefined') return;

    this.connectionTestInterval = setInterval(() => {
      this.testConnectionLatency();
    }, 30000); // Test every 30 seconds
  }

  private async testConnectionLatency(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Use a small API call to test latency
      await fetch('/api/health', { 
        method: 'HEAD',
        cache: 'no-cache',
      });
      
      const latency = Date.now() - startTime;
      this.updateConnectionQuality(latency);
    } catch (error) {
      // Connection failed
      this.updateConnectionQuality(5000, 1); // High latency, packet loss
    }
  }

  private calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private calculateOptimisticAccuracy(): number {
    // Calculate how often optimistic updates were correct
    const optimisticMetrics = this.metrics.filter(m => 
      m.metadata?.optimistic === true
    );
    
    if (optimisticMetrics.length === 0) return 1;
    
    const successful = optimisticMetrics.filter(m => m.success);
    return successful.length / optimisticMetrics.length;
  }

  private estimateBandwidth(latency: number): number {
    // Rough estimation based on latency
    if (latency < 50) return 100; // High bandwidth
    if (latency < 200) return 50; // Medium bandwidth
    if (latency < 500) return 10; // Low bandwidth
    return 1; // Very low bandwidth
  }

  private calculateStability(latency: number, packetLoss: number): ConnectionQuality['stability'] {
    if (latency < 100 && packetLoss < 0.01) return 'excellent';
    if (latency < 300 && packetLoss < 0.05) return 'good';
    if (latency < 1000 && packetLoss < 0.1) return 'fair';
    return 'poor';
  }

  private estimateMemoryUsage(): number {
    if (typeof window === 'undefined' || !('performance' in window)) {
      return 0;
    }

    // @ts-ignore - memory API might not be available in all browsers
    const memory = (performance as any).memory;
    return memory ? memory.usedJSHeapSize : 0;
  }

  private calculateScrollPerformance(): number {
    // Placeholder - would need actual scroll event tracking
    return 60; // Assume 60fps
  }

  private calculateInputLatency(): number {
    const inputMetrics = this.metrics.filter(m => 
      m.type === 'ui_response' && m.metadata?.action?.includes('input')
    );
    return this.calculateAverage(inputMetrics.map(m => m.duration || 0));
  }

  private calculateFrameRate(): number {
    // Placeholder - would need actual frame rate monitoring
    return 60;
  }

  private calculateOverallHealth(): 'excellent' | 'good' | 'fair' | 'poor' {
    const messageStats = this.getMessageDeliveryStats();
    const cacheStats = this.getCachePerformanceStats();
    const connectionQuality = this.getConnectionQuality();

    const scores = [
      messageStats.successRate,
      cacheStats.hitRate,
      connectionQuality.stability === 'excellent' ? 1 : 
      connectionQuality.stability === 'good' ? 0.8 :
      connectionQuality.stability === 'fair' ? 0.6 : 0.4,
    ];

    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

    if (averageScore >= 0.9) return 'excellent';
    if (averageScore >= 0.7) return 'good';
    if (averageScore >= 0.5) return 'fair';
    return 'poor';
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
    
    if (this.connectionTestInterval) {
      clearInterval(this.connectionTestInterval);
    }
  }
}

// Export singleton instance
export const messagePerformanceMonitor = new MessagePerformanceMonitor();

// Auto-cleanup old metrics every hour
if (typeof window !== 'undefined') {
  setInterval(() => {
    messagePerformanceMonitor.clearOldMetrics();
  }, 60 * 60 * 1000);
}

export type { 
  PerformanceMetric, 
  ConnectionQuality, 
  MessageDeliveryStats, 
  CachePerformanceStats,
  UIPerformanceStats 
};
