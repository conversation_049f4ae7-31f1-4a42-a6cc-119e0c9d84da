const { execSync } = require('child_process');
const path = require('path');

console.log('Initializing site settings...');

try {
  // Run the TypeScript initialization script
  execSync('npx tsx src/scripts/init-site-settings.ts', {
    stdio: 'inherit',
    cwd: process.cwd()
  });
  
  console.log('Site settings initialized successfully!');
} catch (error) {
  console.error('Error initializing site settings:', error.message);
  process.exit(1);
}
