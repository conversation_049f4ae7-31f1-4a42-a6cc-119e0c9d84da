/**
 * Uploads a file to Cloudinary via our API route with retry logic
 * @param file The file to upload
 * @param maxRetries Maximum number of retry attempts
 * @returns The Cloudinary URL of the uploaded file
 */
export async function uploadToCloudinary(file: File, maxRetries: number = 3): Promise<string> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Validate file before upload
      if (!file || file.size === 0) {
        throw new Error('Invalid file: File is empty or corrupted');
      }

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size too large. Maximum size is 10MB');
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        throw new Error('Invalid file type. Only images are allowed');
      }

      const formData = new FormData();
      formData.append('files', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(`Upload failed: ${errorData.message || response.statusText}`);
      }

      const data = await response.json();

      // Validate response data
      if (!data.urls || !Array.isArray(data.urls) || data.urls.length === 0) {
        throw new Error('Invalid response: No URLs returned from upload');
      }

      const uploadedUrl = data.urls[0];
      if (!uploadedUrl || typeof uploadedUrl !== 'string') {
        throw new Error('Invalid response: Invalid URL returned from upload');
      }

      return uploadedUrl;

    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown upload error');

      // If this is the last attempt, don't wait
      if (attempt === maxRetries) {
        break;
      }

      // Wait before retry (exponential backoff)
      const waitTime = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  throw new Error(`Failed to upload file ${file.name} after ${maxRetries} attempts: ${lastError?.message || 'Unknown error'}`);
}

/**
 * Uploads multiple files to Cloudinary via our API route with improved error handling
 * @param files Array of files to upload
 * @param maxRetries Maximum number of retry attempts per file
 * @param userId User ID for debugging (optional)
 * @returns Array of Cloudinary URLs
 */
export async function uploadMultipleToCloudinary(files: File[], maxRetries: number = 3): Promise<string[]> {
  if (!files || files.length === 0) {
    return [];
  }

  try {
    // Validate files before upload
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (!file || file.size === 0) {
        throw new Error(`File ${i + 1} is empty or invalid`);
      }
      if (file.size > 10 * 1024 * 1024) {
        throw new Error(`File ${file.name} is too large (${file.size} bytes). Maximum size is 10MB`);
      }
      if (!file.type.startsWith('image/')) {
        throw new Error(`File ${file.name} has invalid type (${file.type}). Only images are allowed`);
      }
    }

    // Upload files one by one to avoid overwhelming the server
    // and to provide better error handling per file
    const uploadPromises = files.map(async (file) => {
      try {
        const url = await uploadToCloudinary(file, maxRetries);
        return url;
      } catch (error) {
        throw new Error(`Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    });

    // Wait for all uploads to complete
    const uploadedUrls = await Promise.all(uploadPromises);

    // Validate all URLs
    const validUrls = uploadedUrls.filter(url => url && typeof url === 'string' && url.trim().length > 0);

    if (validUrls.length !== files.length) {
      throw new Error(`Upload validation failed: Expected ${files.length} URLs, got ${validUrls.length}`);
    }

    return validUrls;

  } catch (error) {
    throw new Error(`Failed to upload images: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
