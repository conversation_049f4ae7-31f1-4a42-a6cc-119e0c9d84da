import Link from "next/link";
import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { Button } from "@/components/ui/Button";
import { db } from "@/lib/db";
import { groups, groupMembers, users } from "@/lib/db/schema";
import { eq, and, desc, count } from "drizzle-orm";
import { formatTimeAgo, formatDate } from "@/lib/utils";
import {
  UsersIcon,
  ChevronLeftIcon,
  UserIcon,
  CalendarIcon,
  GlobeAltIcon,
  LockClosedIcon,
  BookOpenIcon,
  InformationCircleIcon,
  Cog6ToothIcon
} from "@heroicons/react/24/outline";

// Import components
import { GroupHeader } from "@/components/groups/GroupHeader";
import { GroupTabs } from "@/components/groups/GroupTabs";
import { GroupNotFound } from "@/components/groups/GroupNotFound";

interface GroupAboutPageProps {
  params: Promise<{
    groupId: string;
  }>;
}

export default async function GroupAboutPage({ params }: GroupAboutPageProps) {
  const user = await requireAuth();
  const resolvedParams = await params;
  const { groupId } = resolvedParams;

  // Fetch the group
  const group = await db.select({
    id: groups.id,
    name: groups.name,
    description: groups.description,
    visibility: groups.visibility,
    coverImage: groups.coverImage,
    category: groups.category,
    creatorId: groups.creatorId,
    createdAt: groups.createdAt,
    updatedAt: groups.updatedAt
  })
  .from(groups)
  .where(eq(groups.id, groupId))
  .limit(1)
  .then(results => results[0]);

  // Fetch creator info
  const creator = group ? await db.query.users.findFirst({
    where: eq(users.id, group.creatorId),
    columns: {
      id: true,
      name: true,
      username: true,
      image: true,
    },
  }) : null;

  if (!group) {
    // If group doesn't exist, show a nice error message
    return (
      <MainLayout>
        <GroupNotFound />
      </MainLayout>
    );
  }

  // Get user's role in the group
  const userMembership = await db.query.groupMembers.findFirst({
    where: and(
      eq(groupMembers.groupId, groupId),
      eq(groupMembers.userId, user.id)
    ),
  });

  // Check if user has access to the group
  const isPublic = group.visibility === "public";
  const isPrivateVisible = group.visibility === "private-visible";
  const isMember = userMembership && (userMembership.role === "admin" || userMembership.role === "moderator" || userMembership.role === "member");
  const isAdmin = userMembership && userMembership.role === "admin";
  const isModerator = userMembership && userMembership.role === "moderator";
  const isCreator = group.creatorId === user.id;

  // For private-hidden groups, only members can see the about page
  if (group.visibility === "private-hidden" && !isMember && !isCreator) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Link href="/groups">
              <Button variant="outline" size="sm">
                <ChevronLeftIcon className="mr-1 h-4 w-4" />
                Back to Groups
              </Button>
            </Link>
          </div>

          <div className="rounded-lg bg-white p-8 text-center shadow">
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100">
              <UsersIcon className="h-8 w-8 text-yellow-600" />
            </div>
            <h1 className="mt-4 text-xl font-bold text-gray-900">
              Private Group
            </h1>
            <p className="mt-4 text-sm text-gray-500">
              This is a private group. You need to be a member to view its details.
            </p>
            <div className="mt-4">
              <Link href={`/groups/${groupId}`}>
                <Button>
                  View Group
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Get member count
  const membersCount = await db
    .select({ count: count() })
    .from(groupMembers)
    .where(eq(groupMembers.groupId, groupId));

  // Get admin count
  const adminsCount = await db
    .select({ count: count() })
    .from(groupMembers)
    .where(and(
      eq(groupMembers.groupId, groupId),
      eq(groupMembers.role, "admin")
    ));

  // Get moderator count
  const moderatorsCount = await db
    .select({ count: count() })
    .from(groupMembers)
    .where(and(
      eq(groupMembers.groupId, groupId),
      eq(groupMembers.role, "moderator")
    ));

  return (
    <MainLayout>
      {/* Hero section with cover image */}
      <GroupHeader
        group={group}
        membersCount={membersCount[0]?.count || 0}
        isCreator={isCreator}
        isAdmin={isAdmin || false}
        isMember={isMember || false}
        isPending={false}
        isPublic={isPublic}
        isPrivateVisible={isPrivateVisible}
      />

      {/* Tabs navigation */}
      <GroupTabs groupId={groupId} activeTab="about" />

      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {/* Main content */}
          <div className="md:col-span-2 space-y-6">
            {/* About section */}
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h2 className="text-lg font-medium text-gray-900 mb-4">About This Group</h2>

              {group.description ? (
                <div className="prose prose-sm max-w-none text-gray-600">
                  <p>{group.description}</p>
                </div>
              ) : (
                <p className="text-sm italic text-gray-400">No description provided</p>
              )}

              <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <CalendarIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-gray-900">Created</h3>
                    <p className="text-sm text-gray-500">{formatDate(group.createdAt)}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {group.visibility === "public" ? (
                      <GlobeAltIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <LockClosedIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-gray-900">Privacy</h3>
                    <p className="text-sm text-gray-500">
                      {group.visibility === "public"
                        ? "Public - Anyone can see the group and its posts"
                        : group.visibility === "private-visible"
                          ? "Private (Visible) - Anyone can find the group, but only members can see posts"
                          : "Private (Hidden) - Only members can find the group and see posts"}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <UserIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-gray-900">Created by</h3>
                    <p className="text-sm text-gray-500">
                      <Link href={`/user/${creator?.username || group.creatorId}`} className="text-blue-600 hover:underline">
                        {creator?.name || 'Unknown'}
                      </Link>
                    </p>
                  </div>
                </div>

                {group.category && (
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <InformationCircleIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-gray-900">Category</h3>
                      <p className="text-sm text-gray-500">{group.category}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Rules section */}
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <BookOpenIcon className="mr-2 h-5 w-5 text-gray-500" />
                Group Rules
              </h2>

              {/* Rules are not yet implemented */}
              {false ? (
                <div className="prose prose-sm max-w-none text-gray-600">
                  <p className="whitespace-pre-line">Rules would go here</p>
                </div>
              ) : (
                <p className="text-sm italic text-gray-400">No rules have been set for this group</p>
              )}

              {(isAdmin || isCreator) && (
                <div className="mt-4">
                  <Button variant="outline" size="sm">
                    <Cog6ToothIcon className="mr-1.5 h-4 w-4" />
                    Edit Rules
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Group stats */}
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Group Stats</h2>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Members</span>
                  <span className="text-sm font-medium text-gray-900">{membersCount[0]?.count || 0}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Admins</span>
                  <span className="text-sm font-medium text-gray-900">{adminsCount[0]?.count || 0}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Moderators</span>
                  <span className="text-sm font-medium text-gray-900">{moderatorsCount[0]?.count || 0}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Created</span>
                  <span className="text-sm font-medium text-gray-900">{formatTimeAgo(group.createdAt)}</span>
                </div>
              </div>
            </div>

            {/* Admin actions */}
            {(isAdmin || isCreator) && (
              <div className="rounded-lg bg-white p-6 shadow-sm">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Admin Actions</h2>

                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <Cog6ToothIcon className="mr-1.5 h-5 w-5" />
                    Group Settings
                  </Button>

                  <Button variant="outline" className="w-full justify-start">
                    <UsersIcon className="mr-1.5 h-5 w-5" />
                    Manage Members
                  </Button>

                  {isCreator && (
                    <Button variant="danger" className="w-full justify-start">
                      Delete Group
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
