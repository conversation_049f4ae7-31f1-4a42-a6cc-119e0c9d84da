import mysql from 'mysql2/promise';
import { v4 as uuidv4 } from 'uuid';
import { dbConfig } from '../lib/config';

async function initSiteSettings() {
  console.log("Initializing site settings...");
  
  // Create connection
  const connection = await mysql.createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });
  
  console.log("Connected to database successfully!");
  
  try {
    // Check if site_settings table exists
    const [siteSettingsExists] = await connection.execute(
      "SHOW TABLES LIKE 'site_settings'"
    );

    if (Array.isArray(siteSettingsExists) && siteSettingsExists.length === 0) {
      console.log("Creating site_settings table...");
      
      // Create site_settings table
      await connection.execute(`
        CREATE TABLE site_settings (
          id VARCHAR(255) PRIMARY KEY,
          setting_key VARCHAR(100) NOT NULL UNIQUE,
          value TEXT,
          type VARCHAR(50) NOT NULL,
          group_name VARCHAR(100) NOT NULL,
          label VARCHAR(255) NOT NULL,
          description TEXT,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      console.log("site_settings table created successfully!");
    } else {
      console.log("site_settings table already exists.");
    }

    // Define all default settings
    const defaultSettings = [
      // General settings
      { key: 'site_title', value: 'HIFNF', type: 'text', group_name: 'general', label: 'Site Title', description: 'The title of the website' },
      { key: 'site_description', value: 'A social media platform to connect with friends and share your life', type: 'textarea', group_name: 'general', label: 'Site Description', description: 'A brief description of the website' },
      { key: 'logo_url', value: '/logo.png', type: 'image', group_name: 'general', label: 'Logo URL', description: 'The URL of the site logo' },
      { key: 'favicon_url', value: '/favicon.ico', type: 'image', group_name: 'general', label: 'Favicon URL', description: 'The URL of the site favicon' },
      { key: 'maintenance_mode', value: 'false', type: 'boolean', group_name: 'general', label: 'Maintenance Mode', description: 'Enable maintenance mode to show a maintenance page' },

      // Theme settings
      { key: 'default_theme', value: 'light', type: 'select', group_name: 'theme', label: 'Default Theme', description: 'The default theme for new users' },
      { key: 'primary_color', value: '#3b82f6', type: 'color', group_name: 'theme', label: 'Primary Color', description: 'The primary color of the website' },
      { key: 'allow_user_theme_toggle', value: 'true', type: 'boolean', group_name: 'theme', label: 'Allow User Theme Toggle', description: 'Allow users to switch between light and dark themes' },

      // Feature settings
      { key: 'enable_groups', value: 'true', type: 'boolean', group_name: 'features', label: 'Enable Groups', description: 'Enable the groups feature' },
      { key: 'enable_events', value: 'true', type: 'boolean', group_name: 'features', label: 'Enable Events', description: 'Enable the events feature' },
      { key: 'enable_marketplace', value: 'true', type: 'boolean', group_name: 'features', label: 'Enable Marketplace', description: 'Enable the marketplace feature' },
      { key: 'enable_pages', value: 'true', type: 'boolean', group_name: 'features', label: 'Enable Pages', description: 'Enable the fan pages feature' },

      // Content settings
      { key: 'allow_image_uploads', value: 'true', type: 'boolean', group_name: 'content', label: 'Allow Image Uploads', description: 'Allow users to upload images' },
      { key: 'allow_video_uploads', value: 'true', type: 'boolean', group_name: 'content', label: 'Allow Video Uploads', description: 'Allow users to upload videos' },
      { key: 'max_upload_size', value: '10', type: 'number', group_name: 'content', label: 'Max Upload Size (MB)', description: 'Maximum file size for uploads in megabytes' },

      // OAuth settings
      { key: 'google_client_id', value: '', type: 'text', group_name: 'oauth', label: 'Google Client ID', description: 'Google OAuth client ID' },
      { key: 'google_client_secret', value: '', type: 'password', group_name: 'oauth', label: 'Google Client Secret', description: 'Google OAuth client secret' },
      { key: 'google_oauth_enabled', value: 'false', type: 'boolean', group_name: 'oauth', label: 'Enable Google OAuth', description: 'Enable Google OAuth login' },
      { key: 'github_client_id', value: '', type: 'text', group_name: 'oauth', label: 'GitHub Client ID', description: 'GitHub OAuth client ID' },
      { key: 'github_client_secret', value: '', type: 'password', group_name: 'oauth', label: 'GitHub Client Secret', description: 'GitHub OAuth client secret' },
      { key: 'github_oauth_enabled', value: 'false', type: 'boolean', group_name: 'oauth', label: 'Enable GitHub OAuth', description: 'Enable GitHub OAuth login' },

      // Email settings
      { key: 'email_from_address', value: '<EMAIL>', type: 'email', group_name: 'email', label: 'From Email Address', description: 'The email address used to send emails' },
      { key: 'email_from_name', value: 'HIFNF', type: 'text', group_name: 'email', label: 'From Name', description: 'The name used to send emails' },

      // UI settings
      { key: 'show_online_status', value: 'true', type: 'boolean', group_name: 'ui', label: 'Show Online Status', description: 'Show online status indicators for users' },
      { key: 'enable_dark_mode', value: 'true', type: 'boolean', group_name: 'ui', label: 'Enable Dark Mode', description: 'Enable dark mode theme option' },
      { key: 'show_user_avatars', value: 'true', type: 'boolean', group_name: 'ui', label: 'Show User Avatars', description: 'Show user profile pictures throughout the site' },
      { key: 'enable_notifications', value: 'true', type: 'boolean', group_name: 'ui', label: 'Enable Notifications', description: 'Enable real-time notifications' },
    ];

    // Check and insert each setting
    for (const setting of defaultSettings) {
      // Check if the setting already exists
      const [existingSetting] = await connection.execute(
        "SELECT * FROM site_settings WHERE setting_key = ?",
        [setting.key]
      );

      if (Array.isArray(existingSetting) && existingSetting.length === 0) {
        // Insert the setting
        const id = uuidv4();
        await connection.execute(`
          INSERT INTO site_settings (id, setting_key, value, type, group_name, label, description)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [id, setting.key, setting.value, setting.type, setting.group_name, setting.label, setting.description]);
        
        console.log(`Added setting: ${setting.key}`);
      } else {
        console.log(`Setting already exists: ${setting.key}`);
      }
    }

    console.log("Site settings initialization completed successfully!");
    
  } catch (error) {
    console.error('Error initializing site settings:', error);
    throw error;
  } finally {
    await connection.end();
    console.log("Database connection closed.");
  }
}

// Run the script if called directly
if (require.main === module) {
  initSiteSettings().catch(console.error);
}

export { initSiteSettings };
