import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPagePostLikes, users, fanPagePosts } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";

interface RouteParams {
  params: Promise<{
    postId: string;
  }>;
}

// GET /api/fan-pages/posts/[postId]/reactions - Get users who reacted to a fan page post
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { postId } = await params;

    // Verify fan page post exists
    const post = await db.query.fanPagePosts.findFirst({
      where: eq(fanPagePosts.id, postId),
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Fetch users who liked the fan page post
    const likesData = await db
      .select({
        id: users.id,
        name: users.name,
        username: users.username,
        image: users.image,
        createdAt: fanPagePostLikes.createdAt,
      })
      .from(fanPagePostLikes)
      .innerJoin(users, eq(fanPagePostLikes.userId, users.id))
      .where(eq(fanPagePostLikes.fanPagePostId, postId))
      .orderBy(desc(fanPagePostLikes.createdAt));

    // Fan page posts don't have dislikes, so return empty array
    const dislikesData: any[] = [];

    // Format the data
    const likes = likesData.map(user => ({
      ...user,
      reactionType: 'like' as const,
    }));

    const dislikes = dislikesData.map(user => ({
      ...user,
      reactionType: 'dislike' as const,
    }));

    return NextResponse.json({
      likes,
      dislikes,
      total: {
        likes: likes.length,
        dislikes: dislikes.length,
      }
    });

  } catch (error) {
    console.error("Error fetching fan page post reactions:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
