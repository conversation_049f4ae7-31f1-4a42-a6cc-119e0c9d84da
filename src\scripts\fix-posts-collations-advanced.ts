import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log('Starting advanced migration to fix collation issues for posts and related tables...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: dbH<PERSON>,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
  });

  try {
    // Set target collation
    const targetCollation = "utf8mb4_unicode_ci";

    console.log("Step 1: Disabling foreign key checks...");
    await connection.query("SET FOREIGN_KEY_CHECKS = 0");

    console.log("Step 2: Converting posts table...");
    await connection.query(
      `ALTER TABLE posts CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );

    console.log("Step 3: Converting likes table...");
    await connection.query(
      `ALTER TABLE likes CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );

    console.log("Step 4: Converting comments table...");
    await connection.query(
      `ALTER TABLE comments CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );

    console.log("Step 5: Converting groups table (using backticks)...");
    await connection.query(
      `ALTER TABLE \`groups\` CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );

    console.log("Step 6: Re-enabling foreign key checks...");
    await connection.query("SET FOREIGN_KEY_CHECKS = 1");

    console.log("Step 7: Verifying the fix...");
    const [verifyColumns] = await connection.query(
      `SELECT table_name, column_name, collation_name, data_type
       FROM information_schema.COLUMNS
       WHERE table_schema = ? 
       AND table_name IN ('posts', 'users', 'likes', 'comments')
       AND data_type IN ('varchar', 'char', 'text', 'enum')
       AND collation_name != ?
       ORDER BY table_name, column_name`,
      [dbName, targetCollation]
    );
    
    if (Array.isArray(verifyColumns) && verifyColumns.length > 0) {
      console.log("⚠ Warning: Some columns still have different collations:", verifyColumns);
    } else {
      console.log("✓ All key columns now have the correct collation!");
    }

    console.log("Advanced posts collation fix completed successfully!");
  } catch (error) {
    console.error("Error during migration:", error);
    
    // Try to re-enable foreign key checks even if there was an error
    try {
      await connection.query("SET FOREIGN_KEY_CHECKS = 1");
    } catch (fkError) {
      console.error("Error re-enabling foreign key checks:", fkError);
    }
    
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
