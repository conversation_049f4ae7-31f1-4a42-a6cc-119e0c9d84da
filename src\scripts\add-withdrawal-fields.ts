import { db } from '../lib/db';
import mysql from 'mysql2/promise';

async function addWithdrawalFields() {
  try {
    console.log('🚀 Adding withdrawal fields to wallet_transactions table...');

    // Create a direct MySQL connection for DDL operations
    const connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST || 'localhost',
      user: process.env.DATABASE_USERNAME || 'root',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || 'hifnf',
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    // Add adminNote field
    try {
      await connection.query(`
        ALTER TABLE wallet_transactions
        ADD COLUMN adminNote TEXT NULL AFTER note
      `);
      console.log('✅ Added adminNote field');
    } catch (error: any) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('⚠️ adminNote field already exists');
      } else {
        throw error;
      }
    }

    // Add processedAt field
    try {
      await connection.query(`
        ALTER TABLE wallet_transactions
        ADD COLUMN processedAt TIMESTAMP NULL AFTER metadata
      `);
      console.log('✅ Added processedAt field');
    } catch (error: any) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('⚠️ processedAt field already exists');
      } else {
        throw error;
      }
    }

    await connection.end();
    console.log('✅ Withdrawal fields migration completed successfully!');
  } catch (error) {
    console.error('❌ Error adding withdrawal fields:', error);
    process.exit(1);
  }
}

// Run the migration
addWithdrawalFields();
