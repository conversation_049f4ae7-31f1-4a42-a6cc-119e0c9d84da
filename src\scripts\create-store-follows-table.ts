import mysql from "mysql2/promise";
import * as dotenv from "dotenv";

dotenv.config();

async function main() {
  console.log("Creating store_follows table...");

  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || "localhost",
    user: process.env.DATABASE_USERNAME || "root",
    password: process.env.DATABASE_PASSWORD || "",
    database: process.env.DATABASE_NAME || "hifnf_db",
    multipleStatements: true,
  });

  try {
    // Check if store_follows table already exists
    const [storeFollowsTable] = await connection.execute(
      "SHOW TABLES LIKE 'store_follows'"
    );
    
    // @ts-ignore
    if (storeFollowsTable.length > 0) {
      console.log("Store follows table already exists in database.");
    } else {
      console.log("Creating store_follows table...");
      
      // Create store_follows table
      await connection.execute(`
        CREATE TABLE store_follows (
          id VARCHAR(255) PRIMARY KEY,
          userId VARCHAR(255) NOT NULL,
          storeId VARCHAR(255) NOT NULL,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (storeId) REFERENCES stores(id) ON DELETE CASCADE,
          UNIQUE KEY unique_store_follow (userId, storeId)
        )
      `);
      
      console.log("Store follows table created successfully!");
    }

    console.log("Store follows table setup complete!");
  } catch (error) {
    console.error("Error setting up store_follows table:", error);
    process.exit(1);
  } finally {
    await connection.end();
  }
}

main().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});
