import mysql from "mysql2/promise";
import * as dotenv from "dotenv";

dotenv.config();

async function createWalletTables() {
  console.log("Creating wallet system tables...");

  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || "localhost",
    user: process.env.DATABASE_USERNAME || "root",
    password: process.env.DATABASE_PASSWORD || "",
    database: process.env.DATABASE_NAME || "hifnf_db",
    multipleStatements: true,
  });

  try {
    console.log("Creating wallet tables...");

    // Create wallets table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS wallets (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL UNIQUE,
        generalBalance DECIMAL(15, 2) DEFAULT 0.00 NOT NULL,
        earningBalance DECIMAL(15, 2) DEFAULT 0.00 NOT NULL,
        totalDeposited DECIMAL(15, 2) DEFAULT 0.00 NOT NULL,
        totalWithdrawn DECIMAL(15, 2) DEFAULT 0.00 NOT NULL,
        totalSent DECIMAL(15, 2) DEFAULT 0.00 NOT NULL,
        totalReceived DECIMAL(15, 2) DEFAULT 0.00 NOT NULL,
        isActive BOOLEAN DEFAULT TRUE NOT NULL,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log("✅ Wallets table created");

    // Create wallet_transactions table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS wallet_transactions (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL,
        type ENUM('deposit', 'send', 'receive', 'cashout', 'internal_transfer', 'earning', 'withdraw') NOT NULL,
        amount DECIMAL(15, 2) NOT NULL,
        fee DECIMAL(15, 2) DEFAULT 0.00 NOT NULL,
        netAmount DECIMAL(15, 2) NOT NULL,
        walletType ENUM('general', 'earning') NOT NULL,
        toUserId VARCHAR(255),
        toAgentId VARCHAR(255),
        fromUserId VARCHAR(255),
        fromAgentId VARCHAR(255),
        paymentGatewayId VARCHAR(255),
        gatewayTransactionId VARCHAR(255),
        reference VARCHAR(255),
        description TEXT,
        status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending' NOT NULL,
        metadata JSON,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log("✅ Wallet transactions table created");

    // Create wallet_settings table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS wallet_settings (
        id VARCHAR(255) PRIMARY KEY,
        \`key\` VARCHAR(100) NOT NULL UNIQUE,
        value TEXT NOT NULL,
        type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' NOT NULL,
        description TEXT,
        category VARCHAR(100) DEFAULT 'general',
        isSystem BOOLEAN DEFAULT FALSE NOT NULL,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log("✅ Wallet settings table created");

    // Create agents table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS agents (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(255),
        location VARCHAR(255),
        serviceType VARCHAR(100) NOT NULL,
        accountNumber VARCHAR(100) NOT NULL,
        accountName VARCHAR(255) NOT NULL,
        dailyLimit DECIMAL(15, 2) DEFAULT 50000.00 NOT NULL,
        currentDailyAmount DECIMAL(15, 2) DEFAULT 0.00 NOT NULL,
        commission DECIMAL(5, 2) DEFAULT 2.00 NOT NULL,
        isActive BOOLEAN DEFAULT TRUE NOT NULL,
        isVerified BOOLEAN DEFAULT FALSE NOT NULL,
        rating DECIMAL(3, 2) DEFAULT 0.00,
        totalTransactions INT DEFAULT 0,
        totalAmount DECIMAL(15, 2) DEFAULT 0.00,
        lastTransactionAt TIMESTAMP NULL,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log("✅ Agents table created");

    // Create payment_gateways table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS payment_gateways (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        displayName VARCHAR(255) NOT NULL,
        type ENUM('stripe', 'paypal', 'razorpay', 'sslcommerz', 'bkash', 'nagad', 'rocket') NOT NULL,
        isActive BOOLEAN DEFAULT FALSE NOT NULL,
        config JSON NOT NULL,
        depositFee DECIMAL(5, 2) DEFAULT 0.00,
        depositFixedFee DECIMAL(10, 2) DEFAULT 0.00,
        minDeposit DECIMAL(10, 2) DEFAULT 1.00,
        maxDeposit DECIMAL(15, 2) DEFAULT 10000.00,
        currency VARCHAR(10) DEFAULT 'USD',
        supportedCountries JSON,
        sortOrder INT DEFAULT 0,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log("✅ Payment gateways table created");

    // Create pin_codes table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS pin_codes (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL UNIQUE,
        pinHash VARCHAR(255) NOT NULL,
        isActive BOOLEAN DEFAULT TRUE NOT NULL,
        failedAttempts INT DEFAULT 0,
        lockedUntil TIMESTAMP NULL,
        lastUsedAt TIMESTAMP NULL,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log("✅ PIN codes table created");

    // Create cashout_requests table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS cashout_requests (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL,
        agentId VARCHAR(255) NOT NULL,
        amount DECIMAL(15, 2) NOT NULL,
        fee DECIMAL(15, 2) DEFAULT 0.00 NOT NULL,
        netAmount DECIMAL(15, 2) NOT NULL,
        status ENUM('pending', 'accepted', 'completed', 'cancelled', 'rejected') DEFAULT 'pending' NOT NULL,
        agentAccountNumber VARCHAR(100) NOT NULL,
        agentAccountName VARCHAR(255) NOT NULL,
        userPhone VARCHAR(20) NOT NULL,
        notes TEXT,
        agentNotes TEXT,
        completedAt TIMESTAMP NULL,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log("✅ Cashout requests table created");

    console.log("🎉 All wallet tables created successfully!");

  } catch (error) {
    console.error("❌ Error creating wallet tables:", error);
    throw error;
  } finally {
    await connection.end();
  }
}

createWalletTables().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});
