import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function createReferralTables() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'hifnf',
  });

  try {
    console.log('Creating referral system tables...');

    // Create referral_codes table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS referral_codes (
        id VARCHAR(255) PRIMARY KEY,
        userId VARCHAR(255) NOT NULL UNIQUE,
        code VARCHAR(50) NOT NULL UNIQUE,
        isActive BOOLEAN DEFAULT TRUE,
        totalReferrals INT DEFAULT 0,
        totalEarnings DECIMAL(15,2) DEFAULT 0.00,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_code (code),
        INDEX idx_user (userId)
      )
    `);
    console.log("✅ Referral codes table created");

    // Create referrals table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS referrals (
        id VARCHAR(255) PRIMARY KEY,
        referrerId VARCHAR(255) NOT NULL,
        referredUserId VARCHAR(255) NOT NULL,
        referralCode VARCHAR(50) NOT NULL,
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
        rewardAmount DECIMAL(15,2) DEFAULT 5.00,
        paidAt TIMESTAMP NULL,
        completedAt TIMESTAMP NULL,
        metadata JSON,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (referrerId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (referredUserId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (referralCode) REFERENCES referral_codes(code) ON DELETE CASCADE,
        UNIQUE KEY unique_referral (referrerId, referredUserId),
        INDEX idx_referrer (referrerId),
        INDEX idx_referred (referredUserId),
        INDEX idx_code (referralCode),
        INDEX idx_status (status)
      )
    `);
    console.log("✅ Referrals table created");

    // Create referral_settings table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS referral_settings (
        id VARCHAR(255) PRIMARY KEY,
        isEnabled BOOLEAN DEFAULT TRUE,
        rewardAmount DECIMAL(15,2) DEFAULT 5.00,
        minPayoutThreshold DECIMAL(15,2) DEFAULT 10.00,
        requiresVerification BOOLEAN DEFAULT FALSE,
        maxReferralsPerUser INT DEFAULT 100,
        rewardBothUsers BOOLEAN DEFAULT FALSE,
        referredUserReward DECIMAL(15,2) DEFAULT 0.00,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log("✅ Referral settings table created");

    // Insert default referral settings
    await connection.query(`
      INSERT IGNORE INTO referral_settings (
        id, isEnabled, rewardAmount, minPayoutThreshold, 
        requiresVerification, maxReferralsPerUser, 
        rewardBothUsers, referredUserReward
      ) VALUES (
        'default', TRUE, 5.00, 10.00, 
        FALSE, 100, 
        FALSE, 0.00
      )
    `);
    console.log("✅ Default referral settings inserted");

    console.log('✅ All referral tables created successfully!');

  } catch (error) {
    console.error('❌ Error creating referral tables:', error);
  } finally {
    await connection.end();
  }
}

// Run the script
createReferralTables().catch(console.error);
