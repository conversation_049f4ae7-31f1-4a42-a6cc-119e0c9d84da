import { createConnection } from 'mysql2/promise';
import { config } from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

// Load environment variables
config();

async function main() {
  console.log('Starting UI settings setup...');

  const connection = await createConnection({
    host: process.env.DATABASE_HOST,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    port: parseInt(process.env.DATABASE_PORT || '3306'),
  });

  try {
    // Check if site_settings table exists
    const [siteSettingsExists] = await connection.execute(
      "SHOW TABLES LIKE 'site_settings'"
    );

    if (Array.isArray(siteSettingsExists) && siteSettingsExists.length === 0) {
      console.log("site_settings table doesn't exist. Please run add-admin-tables script first.");
      return;
    }

    // Define UI settings
    const uiSettings = [
      // Layout settings
      { key: 'layout_width', value: '1280px', type: 'text', group_name: 'ui', label: 'Layout Width', description: 'Maximum width of the main content' },
      { key: 'sidebar_width', value: '280px', type: 'text', group_name: 'ui', label: 'Sidebar Width', description: 'Width of the sidebar' },
      { key: 'enable_right_sidebar', value: 'true', type: 'boolean', group_name: 'ui', label: 'Enable Right Sidebar', description: 'Show the right sidebar on applicable pages' },
      { key: 'content_padding', value: '16px', type: 'text', group_name: 'ui', label: 'Content Padding', description: 'Padding around content areas' },
      
      // Colors settings
      { key: 'primary_color', value: '#3b82f6', type: 'color', group_name: 'ui', label: 'Primary Color', description: 'Main brand color used for buttons, links, and accents' },
      { key: 'secondary_color', value: '#10b981', type: 'color', group_name: 'ui', label: 'Secondary Color', description: 'Secondary brand color used for complementary elements' },
      { key: 'background_color', value: '#f3f4f6', type: 'color', group_name: 'ui', label: 'Background Color', description: 'Main background color for the site' },
      { key: 'text_color', value: '#111827', type: 'color', group_name: 'ui', label: 'Text Color', description: 'Main text color for the site' },
      { key: 'link_color', value: '#2563eb', type: 'color', group_name: 'ui', label: 'Link Color', description: 'Color used for links and interactive elements' },
      
      // Typography settings
      { key: 'base_font_size', value: '16px', type: 'text', group_name: 'ui', label: 'Base Font Size', description: 'Base font size for the site' },
      { key: 'heading_font', value: 'Inter, sans-serif', type: 'select', group_name: 'ui', label: 'Heading Font', description: 'Font used for headings' },
      { key: 'body_font', value: 'Inter, sans-serif', type: 'select', group_name: 'ui', label: 'Body Font', description: 'Font used for body text' },
      
      // Components settings
      { key: 'button_radius', value: '6px', type: 'text', group_name: 'ui', label: 'Button Border Radius', description: 'Border radius for buttons' },
      { key: 'card_radius', value: '8px', type: 'text', group_name: 'ui', label: 'Card Border Radius', description: 'Border radius for cards' },
      { key: 'input_radius', value: '6px', type: 'text', group_name: 'ui', label: 'Input Border Radius', description: 'Border radius for input fields' },
      { key: 'shadow_intensity', value: 'medium', type: 'select', group_name: 'ui', label: 'Shadow Intensity', description: 'Intensity of shadows on cards and components' },
    ];

    // Check and insert each UI setting
    for (const setting of uiSettings) {
      // Check if the setting already exists
      const [existingSetting] = await connection.execute(
        "SELECT * FROM site_settings WHERE setting_key = ?",
        [setting.key]
      );

      // @ts-ignore
      if (Array.isArray(existingSetting) && existingSetting.length === 0) {
        // Insert the setting
        const id = uuidv4();
        await connection.execute(`
          INSERT INTO site_settings (id, setting_key, value, type, group_name, label, description)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [id, setting.key, setting.value, setting.type, setting.group_name, setting.label, setting.description]);
        
        console.log(`Added UI setting: ${setting.key}`);
      } else {
        console.log(`UI setting already exists: ${setting.key}`);
      }
    }

    console.log('UI settings setup completed successfully!');
  } catch (error) {
    console.error('Error setting up UI settings:', error);
  } finally {
    await connection.end();
  }
}

main().catch(console.error);
