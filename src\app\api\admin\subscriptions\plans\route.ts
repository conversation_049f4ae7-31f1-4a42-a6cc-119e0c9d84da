import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { subscriptionPlans } from "@/lib/db/schema";
import { desc, eq } from "drizzle-orm";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

// Validation schema for creating/updating subscription plans
const planSchema = z.object({
  name: z.string().min(2).max(100),
  displayName: z.string().min(2).max(100),
  description: z.string().optional(),
  price: z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid price format"),
  currency: z.string().length(3).default("USD"),
  billingCycle: z.enum(['monthly', 'yearly']).default('monthly'),
  features: z.array(z.string()).default([]),
  maxPosts: z.number().int().default(-1),
  maxStorage: z.number().int().default(-1),
  maxGroups: z.number().int().default(-1),
  canCreateFanPages: z.boolean().default(false),
  canCreateStores: z.boolean().default(false),
  canMonetizeBlogs: z.boolean().default(false),
  prioritySupport: z.boolean().default(false),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().default(0),
});

// GET - Fetch all subscription plans
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const plans = await db.query.subscriptionPlans.findMany({
      orderBy: [desc(subscriptionPlans.sortOrder), desc(subscriptionPlans.createdAt)],
    });

    return NextResponse.json({
      success: true,
      plans,
    });
  } catch (error) {
    console.error("Error fetching subscription plans:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to fetch subscription plans",
      },
      { status: 500 }
    );
  }
}

// POST - Create new subscription plan
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = planSchema.parse(body);

    // Check if plan name already exists
    const existingPlan = await db.query.subscriptionPlans.findFirst({
      where: eq(subscriptionPlans.name, validatedData.name),
    });

    if (existingPlan) {
      return NextResponse.json(
        {
          success: false,
          message: "A plan with this name already exists",
        },
        { status: 400 }
      );
    }

    // Get the highest sort order and increment
    const highestSortOrder = await db.query.subscriptionPlans.findFirst({
      orderBy: [desc(subscriptionPlans.sortOrder)],
    });

    const newSortOrder = highestSortOrder ? highestSortOrder.sortOrder + 1 : 1;

    const planId = uuidv4();
    await db.insert(subscriptionPlans).values({
      id: planId,
      ...validatedData,
      sortOrder: newSortOrder,
    });

    const newPlan = await db.query.subscriptionPlans.findFirst({
      where: eq(subscriptionPlans.id, planId),
    });

    return NextResponse.json(
      {
        success: true,
        message: "Subscription plan created successfully",
        plan: newPlan,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating subscription plan:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Validation error",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to create subscription plan",
      },
      { status: 500 }
    );
  }
}

// DELETE - Delete all plans (bulk operation)
export async function DELETE(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const planIds = searchParams.get('ids')?.split(',') || [];

    if (planIds.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: "No plan IDs provided",
        },
        { status: 400 }
      );
    }

    // Check if any of the plans have active subscriptions
    // This would require checking the userSubscriptions table
    // For now, we'll allow deletion but in production you'd want to prevent this

    for (const planId of planIds) {
      await db.delete(subscriptionPlans).where(eq(subscriptionPlans.id, planId));
    }

    return NextResponse.json({
      success: true,
      message: `${planIds.length} subscription plan(s) deleted successfully`,
    });
  } catch (error) {
    console.error("Error deleting subscription plans:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to delete subscription plans",
      },
      { status: 500 }
    );
  }
}
