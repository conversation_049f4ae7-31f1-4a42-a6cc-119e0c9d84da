import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { SubscriptionService } from "./subscriptionService";

export interface FeatureGateOptions {
  feature: string;
  redirectTo?: string;
  message?: string;
  allowFree?: boolean;
}

// Middleware to check if user has access to a feature
export async function requireSubscriptionFeature(
  request: NextRequest,
  options: FeatureGateOptions
): Promise<NextResponse | null> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.redirect(new URL('/auth/signin', request.url));
    }

    // Check if user can access the feature
    const canAccess = await SubscriptionService.canAccessFeature(
      session.user.id,
      options.feature
    );

    if (!canAccess && !options.allowFree) {
      const redirectUrl = options.redirectTo || '/manage-subscription';
      const response = NextResponse.redirect(new URL(redirectUrl, request.url));
      
      // Add a query parameter to show upgrade message
      if (options.message) {
        const url = new URL(redirectUrl, request.url);
        url.searchParams.set('upgrade_required', 'true');
        url.searchParams.set('feature', options.feature);
        url.searchParams.set('message', options.message);
        return NextResponse.redirect(url);
      }
      
      return response;
    }

    return null; // Allow access
  } catch (error) {
    console.error('Error checking subscription feature:', error);
    return NextResponse.redirect(new URL('/manage-subscription', request.url));
  }
}

// Helper function to check feature access in API routes
export async function checkFeatureAccess(
  userId: string,
  feature: string
): Promise<{ hasAccess: boolean; plan?: any; limits?: any }> {
  try {
    const hasAccess = await SubscriptionService.canAccessFeature(userId, feature);
    const status = await SubscriptionService.getUserSubscriptionStatus(userId);
    const limits = await SubscriptionService.getFeatureLimits(userId);

    return {
      hasAccess,
      plan: status.plan,
      limits,
    };
  } catch (error) {
    console.error('Error checking feature access:', error);
    return { hasAccess: false };
  }
}

// Feature gate component wrapper
export function withSubscriptionFeature<T extends object>(
  WrappedComponent: React.ComponentType<T>,
  feature: string,
  fallbackComponent?: React.ComponentType<T>
) {
  return function FeatureGatedComponent(props: T) {
    // This would be used on the client side with a hook
    // For now, we'll implement the hook separately
    return <WrappedComponent {...props} />;
  };
}

// Common feature checks
export const FeatureGates = {
  CREATE_FAN_PAGES: 'create_fan_pages',
  CREATE_STORES: 'create_stores',
  MONETIZE_BLOGS: 'monetize_blogs',
  PRIORITY_SUPPORT: 'priority_support',
  UNLIMITED_POSTS: 'unlimited_posts',
  UNLIMITED_STORAGE: 'unlimited_storage',
  UNLIMITED_GROUPS: 'unlimited_groups',
} as const;

// Usage limits checker
export async function checkUsageLimits(
  userId: string,
  resource: 'posts' | 'storage' | 'groups'
): Promise<{
  isWithinLimit: boolean;
  current: number;
  limit: number;
  percentage: number;
}> {
  try {
    const limits = await SubscriptionService.getFeatureLimits(userId);
    
    // This would need to be implemented with actual usage tracking
    // For now, we'll return mock data
    const mockUsage = {
      posts: 5,
      storage: 50, // MB
      groups: 1,
    };

    const current = mockUsage[resource];
    const limit = limits[`max${resource.charAt(0).toUpperCase() + resource.slice(1)}`] || 0;
    
    if (limit === -1) {
      // Unlimited
      return {
        isWithinLimit: true,
        current,
        limit: -1,
        percentage: 0,
      };
    }

    const percentage = limit > 0 ? (current / limit) * 100 : 100;
    const isWithinLimit = current < limit;

    return {
      isWithinLimit,
      current,
      limit,
      percentage,
    };
  } catch (error) {
    console.error('Error checking usage limits:', error);
    return {
      isWithinLimit: false,
      current: 0,
      limit: 0,
      percentage: 100,
    };
  }
}

// Subscription status checker for components
export async function getSubscriptionContext(userId: string) {
  try {
    const status = await SubscriptionService.getUserSubscriptionStatus(userId);
    const limits = await SubscriptionService.getFeatureLimits(userId);
    
    return {
      ...status,
      limits,
      features: {
        canCreateFanPages: await SubscriptionService.canAccessFeature(userId, FeatureGates.CREATE_FAN_PAGES),
        canCreateStores: await SubscriptionService.canAccessFeature(userId, FeatureGates.CREATE_STORES),
        canMonetizeBlogs: await SubscriptionService.canAccessFeature(userId, FeatureGates.MONETIZE_BLOGS),
        hasPrioritySupport: await SubscriptionService.canAccessFeature(userId, FeatureGates.PRIORITY_SUPPORT),
        hasUnlimitedPosts: await SubscriptionService.canAccessFeature(userId, FeatureGates.UNLIMITED_POSTS),
        hasUnlimitedStorage: await SubscriptionService.canAccessFeature(userId, FeatureGates.UNLIMITED_STORAGE),
        hasUnlimitedGroups: await SubscriptionService.canAccessFeature(userId, FeatureGates.UNLIMITED_GROUPS),
      },
    };
  } catch (error) {
    console.error('Error getting subscription context:', error);
    return null;
  }
}
