import { createConnection } from 'mysql2/promise';
import { config } from 'dotenv';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';

// Load environment variables
config();

async function main() {
  console.log('Starting admin tables setup...');

  const connection = await createConnection({
    host: process.env.DATABASE_HOST,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    port: parseInt(process.env.DATABASE_PORT || '3306'),
  });

  try {
    // Check if admin_roles table exists
    const [adminRolesExists] = await connection.execute(
      "SHOW TABLES LIKE 'admin_roles'"
    );

    if (Array.isArray(adminRolesExists) && adminRolesExists.length === 0) {
      // Create admin_roles table
      await connection.execute(`
        CREATE TABLE admin_roles (
          id VARCHAR(255) PRIMARY KEY,
          name VARCHAR(100) NOT NULL UNIQUE,
          description TEXT,
          is_system BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      console.log("admin_roles table created successfully!");

      // Insert default super admin role
      const superAdminRoleId = uuidv4();
      await connection.execute(`
        INSERT INTO admin_roles (id, name, description, is_system)
        VALUES (?, 'Super Admin', 'Has full access to all admin features', TRUE)
      `, [superAdminRoleId]);

      console.log("Default Super Admin role created!");
    }

    // Check if admin_permissions table exists
    const [adminPermissionsExists] = await connection.execute(
      "SHOW TABLES LIKE 'admin_permissions'"
    );

    if (Array.isArray(adminPermissionsExists) && adminPermissionsExists.length === 0) {
      // Create admin_permissions table
      await connection.execute(`
        CREATE TABLE admin_permissions (
          id VARCHAR(255) PRIMARY KEY,
          name VARCHAR(100) NOT NULL UNIQUE,
          code VARCHAR(100) NOT NULL UNIQUE,
          description TEXT,
          module VARCHAR(100) NOT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
      `);

      console.log("admin_permissions table created successfully!");

      // Insert default permissions
      const permissions = [
        // Dashboard
        { name: 'View Dashboard', code: 'dashboard.view', module: 'dashboard', description: 'Can view admin dashboard' },

        // User Management
        { name: 'View Users', code: 'users.view', module: 'users', description: 'Can view user list' },
        { name: 'Edit Users', code: 'users.edit', module: 'users', description: 'Can edit user details' },
        { name: 'Ban Users', code: 'users.ban', module: 'users', description: 'Can ban/unban users' },
        { name: 'Delete Users', code: 'users.delete', module: 'users', description: 'Can delete users' },

        // Posts Management
        { name: 'View Posts', code: 'posts.view', module: 'posts', description: 'Can view all posts' },
        { name: 'Edit Posts', code: 'posts.edit', module: 'posts', description: 'Can edit posts' },
        { name: 'Delete Posts', code: 'posts.delete', module: 'posts', description: 'Can delete posts' },

        // Groups Management
        { name: 'View Groups', code: 'groups.view', module: 'groups', description: 'Can view all groups' },
        { name: 'Edit Groups', code: 'groups.edit', module: 'groups', description: 'Can edit groups' },
        { name: 'Delete Groups', code: 'groups.delete', module: 'groups', description: 'Can delete groups' },

        // Events Management
        { name: 'View Events', code: 'events.view', module: 'events', description: 'Can view all events' },
        { name: 'Edit Events', code: 'events.edit', module: 'events', description: 'Can edit events' },
        { name: 'Delete Events', code: 'events.delete', module: 'events', description: 'Can delete events' },

        // Marketplace Management
        { name: 'View Products', code: 'marketplace.products.view', module: 'marketplace', description: 'Can view all products' },
        { name: 'Edit Products', code: 'marketplace.products.edit', module: 'marketplace', description: 'Can edit products' },
        { name: 'Delete Products', code: 'marketplace.products.delete', module: 'marketplace', description: 'Can delete products' },
        { name: 'View Stores', code: 'marketplace.stores.view', module: 'marketplace', description: 'Can view all stores' },
        { name: 'Edit Stores', code: 'marketplace.stores.edit', module: 'marketplace', description: 'Can edit stores' },
        { name: 'Delete Stores', code: 'marketplace.stores.delete', module: 'marketplace', description: 'Can delete stores' },

        // Site Settings
        { name: 'View Settings', code: 'settings.view', module: 'settings', description: 'Can view site settings' },
        { name: 'Edit Settings', code: 'settings.edit', module: 'settings', description: 'Can edit site settings' },

        // Reports & Moderation
        { name: 'View Reports', code: 'reports.view', module: 'reports', description: 'Can view reports' },
        { name: 'Handle Reports', code: 'reports.handle', module: 'reports', description: 'Can handle reports' },

        // Admin Roles
        { name: 'View Roles', code: 'roles.view', module: 'roles', description: 'Can view admin roles' },
        { name: 'Edit Roles', code: 'roles.edit', module: 'roles', description: 'Can edit admin roles' },
        { name: 'Delete Roles', code: 'roles.delete', module: 'roles', description: 'Can delete admin roles' },

        // Wallet System
        { name: 'View Wallets', code: 'wallets.view', module: 'wallets', description: 'Can view user wallets' },
        { name: 'Edit Wallets', code: 'wallets.edit', module: 'wallets', description: 'Can edit user wallets' },

        // Payments & Subscriptions
        { name: 'View Payments', code: 'payments.view', module: 'payments', description: 'Can view payments' },
        { name: 'Edit Payment Settings', code: 'payments.settings', module: 'payments', description: 'Can edit payment settings' },

        // Notifications & Emails
        { name: 'Send Notifications', code: 'notifications.send', module: 'notifications', description: 'Can send global notifications' },
        { name: 'Edit Email Templates', code: 'emails.templates', module: 'emails', description: 'Can edit email templates' },
      ];

      for (const permission of permissions) {
        const permissionId = uuidv4();
        await connection.execute(`
          INSERT INTO admin_permissions (id, name, code, description, module)
          VALUES (?, ?, ?, ?, ?)
        `, [permissionId, permission.name, permission.code, permission.description, permission.module]);
      }

      console.log("Default permissions created!");
    }

    // Check if admin_role_permissions table exists
    const [adminRolePermissionsExists] = await connection.execute(
      "SHOW TABLES LIKE 'admin_role_permissions'"
    );

    if (Array.isArray(adminRolePermissionsExists) && adminRolePermissionsExists.length === 0) {
      // Create admin_role_permissions table
      await connection.execute(`
        CREATE TABLE admin_role_permissions (
          id VARCHAR(255) PRIMARY KEY,
          role_id VARCHAR(255) NOT NULL,
          permission_id VARCHAR(255) NOT NULL,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (role_id) REFERENCES admin_roles(id) ON DELETE CASCADE,
          FOREIGN KEY (permission_id) REFERENCES admin_permissions(id) ON DELETE CASCADE,
          UNIQUE KEY unique_role_permission (role_id, permission_id)
        )
      `);

      console.log("admin_role_permissions table created successfully!");

      // Get super admin role ID
      const [superAdminRole] = await connection.execute(
        "SELECT id FROM admin_roles WHERE name = 'Super Admin'"
      );

      // @ts-ignore
      const superAdminRoleId = superAdminRole[0]?.id;

      // Get all permission IDs
      const [permissions] = await connection.execute(
        "SELECT id FROM admin_permissions"
      );

      // Assign all permissions to super admin role
      // @ts-ignore
      for (const permission of permissions) {
        const id = uuidv4();
        await connection.execute(`
          INSERT INTO admin_role_permissions (id, role_id, permission_id)
          VALUES (?, ?, ?)
        `, [id, superAdminRoleId, permission.id]);
      }

      console.log("Assigned all permissions to Super Admin role!");
    }

    // Check if site_settings table exists
    const [siteSettingsExists] = await connection.execute(
      "SHOW TABLES LIKE 'site_settings'"
    );

    if (Array.isArray(siteSettingsExists) && siteSettingsExists.length === 0) {
      // Create site_settings table
      await connection.execute(`
        CREATE TABLE site_settings (
          id VARCHAR(255) PRIMARY KEY,
          setting_key VARCHAR(100) NOT NULL UNIQUE,
          value TEXT,
          type VARCHAR(50) NOT NULL,
          group_name VARCHAR(100) NOT NULL,
          label VARCHAR(255) NOT NULL,
          description TEXT,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      console.log("site_settings table created successfully!");

      // Insert default settings
      const defaultSettings = [
        // General
        { key: 'site_title', value: 'HIFNF', type: 'text', group_name: 'general', label: 'Site Title', description: 'The title of the website' },
        { key: 'site_description', value: 'A social media platform to connect with friends and share your life', type: 'textarea', group_name: 'general', label: 'Site Description', description: 'A brief description of the website' },
        { key: 'logo_url', value: '/logo.png', type: 'image', group_name: 'general', label: 'Logo URL', description: 'The URL of the site logo' },
        { key: 'favicon_url', value: '/favicon.ico', type: 'image', group_name: 'general', label: 'Favicon URL', description: 'The URL of the site favicon' },
        { key: 'maintenance_mode', value: 'false', type: 'boolean', group_name: 'general', label: 'Maintenance Mode', description: 'Enable maintenance mode' },

        // Theme
        { key: 'default_theme', value: 'light', type: 'select', group_name: 'theme', label: 'Default Theme', description: 'The default theme for the site (light/dark)' },
        { key: 'primary_color', value: '#3b82f6', type: 'color', group_name: 'theme', label: 'Primary Color', description: 'The primary color for the site' },
        { key: 'allow_user_theme_toggle', value: 'true', type: 'boolean', group_name: 'theme', label: 'Allow User Theme Toggle', description: 'Allow users to toggle between light and dark themes' },

        // Features
        { key: 'enable_groups', value: 'true', type: 'boolean', group_name: 'features', label: 'Enable Groups', description: 'Enable the groups feature' },
        { key: 'enable_events', value: 'true', type: 'boolean', group_name: 'features', label: 'Enable Events', description: 'Enable the events feature' },
        { key: 'enable_marketplace', value: 'true', type: 'boolean', group_name: 'features', label: 'Enable Marketplace', description: 'Enable the marketplace feature' },
        { key: 'enable_pages', value: 'true', type: 'boolean', group_name: 'features', label: 'Enable Pages', description: 'Enable the pages feature' },

        // Content
        { key: 'allow_image_uploads', value: 'true', type: 'boolean', group_name: 'content', label: 'Allow Image Uploads', description: 'Allow users to upload images' },
        { key: 'allow_video_uploads', value: 'true', type: 'boolean', group_name: 'content', label: 'Allow Video Uploads', description: 'Allow users to upload videos' },
        { key: 'max_upload_size', value: '10', type: 'number', group_name: 'content', label: 'Max Upload Size (MB)', description: 'Maximum file upload size in MB' },

        // Cloudinary
        { key: 'cloudinary_cloud_name', value: process.env.CLOUDINARY_CLOUD_NAME || '', type: 'text', group_name: 'cloudinary', label: 'Cloudinary Cloud Name', description: 'Your Cloudinary cloud name' },
        { key: 'cloudinary_api_key', value: process.env.CLOUDINARY_API_KEY || '', type: 'text', group_name: 'cloudinary', label: 'Cloudinary API Key', description: 'Your Cloudinary API key' },
        { key: 'cloudinary_api_secret', value: process.env.CLOUDINARY_API_SECRET || '', type: 'text', group_name: 'cloudinary', label: 'Cloudinary API Secret', description: 'Your Cloudinary API secret' },

        // Email
        { key: 'email_from_address', value: '<EMAIL>', type: 'email', group_name: 'email', label: 'From Email Address', description: 'The email address used to send emails' },
        { key: 'email_from_name', value: 'HIFNF', type: 'text', group_name: 'email', label: 'From Name', description: 'The name used to send emails' },
      ];

      for (const setting of defaultSettings) {
        const id = uuidv4();
        await connection.execute(`
          INSERT INTO site_settings (id, setting_key, value, type, group_name, label, description)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [id, setting.key, setting.value, setting.type, setting.group_name, setting.label, setting.description]);
      }

      console.log("Default site settings created!");
    }

    // Add role column to users table if it doesn't exist
    const [userColumns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'role'"
    );

    if (Array.isArray(userColumns) && userColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE users
        ADD COLUMN role VARCHAR(50) DEFAULT 'user' AFTER email
      `);

      console.log("Added role column to users table!");
    }

    // Add is_admin column to users table if it doesn't exist
    const [isAdminColumns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'is_admin'"
    );

    if (Array.isArray(isAdminColumns) && isAdminColumns.length === 0) {
      await connection.execute(`
        ALTER TABLE users
        ADD COLUMN is_admin BOOLEAN DEFAULT FALSE AFTER role
      `);

      console.log("Added is_admin column to users table!");
    }

    // Add admin_role_id column to users table if it doesn't exist
    const [adminRoleIdColumns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'admin_role_id'"
    );

    if (Array.isArray(adminRoleIdColumns) && adminRoleIdColumns.length === 0) {
      // First add the column without the foreign key
      await connection.execute(`
        ALTER TABLE users
        ADD COLUMN admin_role_id VARCHAR(255) NULL AFTER is_admin
      `);

      console.log("Added admin_role_id column to users table!");

      // Then try to add the foreign key
      try {
        await connection.execute(`
          ALTER TABLE users
          ADD FOREIGN KEY (admin_role_id) REFERENCES admin_roles(id) ON DELETE SET NULL
        `);
        console.log("Added foreign key constraint to admin_role_id!");
      } catch (error) {
        console.warn("Could not add foreign key constraint to admin_role_id:", error);
        console.log("Continuing without foreign key constraint...");
      }
    }

    // Create a super admin user if none exists
    const [adminUsers] = await connection.execute(
      "SELECT * FROM users WHERE is_admin = TRUE LIMIT 1"
    );

    if (Array.isArray(adminUsers) && adminUsers.length === 0) {
      // Get super admin role ID
      const [superAdminRole] = await connection.execute(
        "SELECT id FROM admin_roles WHERE name = 'Super Admin'"
      );

      // @ts-ignore
      const superAdminRoleId = superAdminRole[0]?.id;

      // Check if there's at least one user in the system
      const [users] = await connection.execute(
        "SELECT id FROM users LIMIT 1"
      );

      if (Array.isArray(users) && users.length > 0) {
        // @ts-ignore
        const userId = users[0]?.id;

        // Make the first user a super admin
        await connection.execute(`
          UPDATE users
          SET is_admin = TRUE, admin_role_id = ?
          WHERE id = ?
        `, [superAdminRoleId, userId]);

        console.log("Made the first user a super admin!");
      } else {
        // Create a new super admin user
        const adminId = uuidv4();
        const hashedPassword = await bcrypt.hash('admin123', 10);

        await connection.execute(`
          INSERT INTO users (id, name, email, role, is_admin, admin_role_id, createdAt, updatedAt)
          VALUES (?, 'Admin', '<EMAIL>', 'admin', TRUE, ?, NOW(), NOW())
        `, [adminId, superAdminRoleId]);

        console.log("Created a new super admin user!");
        console.log("Email: <EMAIL>");
        console.log("Password: admin123");
      }
    }

    console.log('Admin tables setup completed successfully!');
  } catch (error) {
    console.error('Error setting up admin tables:', error);
  } finally {
    await connection.end();
  }
}

main().catch(console.error);
