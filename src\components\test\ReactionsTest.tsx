"use client";

import { useState } from "react";
import { PostReactions } from "@/components/feed/PostReactions";
import { Button } from "@/components/ui/Button";

export function ReactionsTest() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'likes' | 'dislikes'>('likes');

  // Test with a sample post ID (you can change this to any existing post ID)
  const testPostId = "test-post-id";

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm border max-w-md mx-auto">
      <h3 className="text-lg font-semibold mb-4">Post Reactions Test</h3>
      
      <div className="space-y-3">
        <Button
          onClick={() => {
            setActiveTab('likes');
            setIsModalOpen(true);
          }}
          className="w-full"
        >
          Show Likes
        </Button>
        
        <Button
          onClick={() => {
            setActiveTab('dislikes');
            setIsModalOpen(true);
          }}
          variant="secondary"
          className="w-full"
        >
          Show Dislikes
        </Button>
      </div>

      <PostReactions
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        postId={testPostId}
        postType="user_post"
        initialTab={activeTab}
      />
    </div>
  );
}
