"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

// Import sub-components
import { StoreHeader } from "./store/StoreHeader";
import { StoreStats } from "./store/StoreStats";
import { StoreProducts } from "./store/StoreProducts";
import { StoreReviews } from "./store/StoreReviews";
import { StoreInfoSidebar } from "./store/StoreInfoSidebar";
import { StoreChatSidebar } from "./store/StoreChatSidebar";
import { StoreTabs } from "./store/StoreTabs";
import { StoreInfo } from "./store/StoreInfo";

interface StoreProfileProps {
  store: {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    logo: string | null;
    banner: string | null;
    location: string | null;
    isVerified: boolean | null;
    createdAt: Date;
    owner: {
      id: string;
      name: string | null;
      image: string | null;
    } | null;
    productCount: number;
    followerCount: number;
    reviewCount: number;
    averageRating: number | string | null;
    products: {
      id: string;
      title: string;
      price: number;
      condition: string;
      category: string;
      photos: string[] | null;
      createdAt: Date;
    }[];
    reviews: {
      id: string;
      rating: number;
      comment: string | null;
      createdAt: Date;
      user: {
        id: string;
        name: string | null;
        image: string | null;
      } | null;
    }[];
    isFollowing: boolean;
    isOwner: boolean;
    userReview: {
      id: string;
      rating: number;
      comment: string | null;
    } | null;
  };
}

export function StoreProfile({ store }: StoreProfileProps) {
  const router = useRouter();
  const [isFollowing, setIsFollowing] = useState(store.isFollowing);
  const [followerCount, setFollowerCount] = useState(store.followerCount);
  const [isLoading, setIsLoading] = useState(false);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [reviews, setReviews] = useState(store.reviews);
  const [averageRating, setAverageRating] = useState(store.averageRating);
  const [reviewCount, setReviewCount] = useState(store.reviewCount);
  const [userReview, setUserReview] = useState(store.userReview);
  const [activeTab, setActiveTab] = useState("products");



  // Handle follow/unfollow
  const toggleFollow = async () => {
    try {
      setIsLoading(true);

      const response = await fetch(`/api/marketplace/stores/${store.id}/follow`, {
        method: isFollowing ? "DELETE" : "POST",
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to ${isFollowing ? "unfollow" : "follow"} store`);
      }

      setIsFollowing(!isFollowing);
      setFollowerCount(isFollowing ? followerCount - 1 : followerCount + 1);
    } catch (error) {
      console.error("Error toggling follow:", error);
      alert(`Failed to ${isFollowing ? "unfollow" : "follow"} store. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle review submission
  const handleReviewSubmitted = (newReview: any) => {
    // If it's an update to an existing review
    if (userReview) {
      // Update the reviews list
      setReviews(reviews.map(review =>
        review.user?.id === newReview.user?.id ? newReview : review
      ));
    } else {
      // Add the new review to the list
      setReviews([newReview, ...reviews]);
      setReviewCount(reviewCount + 1);
    }

    // Update user review
    setUserReview({
      id: newReview.id,
      rating: newReview.rating,
      comment: newReview.comment,
    });

    // Update average rating
    setAverageRating(newReview.averageRating);

    // Close the form
    setShowReviewForm(false);
  };

  // Extract unique categories from products
  const categories = Array.from(new Set(
    store.products
      .map(product => product.category)
      .filter(Boolean)
  ));

  return (
    <div className="w-full">
      {/* Store Header - Full Width */}
      <div className="mb-6">
        <StoreHeader
          store={store}
          isFollowing={isFollowing}
          isLoading={isLoading}
          toggleFollow={toggleFollow}
        />
      </div>

      {/* Stats - Full Width */}
      <div className="mb-6">
        <StoreStats
          productCount={store.productCount}
          followerCount={followerCount}
          reviewCount={reviewCount}
          averageRating={averageRating}
        />
      </div>

      {/* Tab Navigation */}
      <div className="mb-6">
        <StoreTabs activeTab={activeTab} onTabChange={setActiveTab} />
      </div>

      {/* Tab Content */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Left Sidebar - Adjust based on active tab */}
        <div className="w-full lg:w-1/4 order-3 lg:order-1">
          {activeTab === "products" && (
            <div className="space-y-6">
              {/* Chat section in left sidebar for Products tab */}
              <StoreChatSidebar
                storeId={store.id}
                storeName={store.name}
                storeLogo={store.logo}
                isOwner={store.isOwner}
              />
              <StoreInfoSidebar
                store={store}
                categories={categories}
              />
            </div>
          )}

          {activeTab === "info" && (
            <div className="space-y-6">
              <StoreChatSidebar
                storeId={store.id}
                storeName={store.name}
                storeLogo={store.logo}
                isOwner={store.isOwner}
              />
              <StoreInfoSidebar
                store={store}
                categories={categories}
              />
            </div>
          )}

          {activeTab === "reviews" && (
            <StoreChatSidebar
              storeId={store.id}
              storeName={store.name}
              storeLogo={store.logo}
              isOwner={store.isOwner}
            />
          )}
        </div>

        {/* Main Content - Adjust width based on active tab */}
        <div className={`w-full ${activeTab === "products" || activeTab === "reviews" ? 'lg:w-3/4' : 'lg:w-1/2'} space-y-6 order-1 lg:order-2`}>
          {activeTab === "products" && (
            <StoreProducts
              storeId={store.id}
              storeName={store.name}
              storeLogo={store.logo}
              products={store.products}
              totalProductCount={store.productCount}
            />
          )}

          {activeTab === "info" && (
            <StoreInfo store={store} />
          )}

          {activeTab === "reviews" && (
            <StoreReviews
              storeId={store.id}
              isOwner={store.isOwner}
              reviews={reviews}
              userReview={userReview}
              onReviewSubmitted={handleReviewSubmitted}
            />
          )}
        </div>

        {/* Right Sidebar - 25% - Only show for Info tab */}
      </div>
    </div>
  );
}
