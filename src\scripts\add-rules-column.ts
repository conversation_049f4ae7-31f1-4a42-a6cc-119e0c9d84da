import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log('Starting migration to add rules column to groups table...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbU<PERSON>,
    password: dbPassword,
    database: dbName,
    port: dbPort,
  });

  try {
    // Check if the rules column already exists
    const [columns] = await connection.execute(
      "SHOW COLUMNS FROM `groups` LIKE 'rules'"
    );

    // @ts-ignore
    if (columns.length > 0) {
      console.log("Rules column already exists in groups table.");
    } else {
      console.log("Adding rules column to groups table...");
      
      // Add the rules column
      await connection.execute(`
        ALTER TABLE \`groups\` 
        ADD COLUMN rules TEXT
      `);
      
      console.log("Rules column added successfully!");
    }
  } catch (error) {
    console.error("Error during migration:", error);
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
