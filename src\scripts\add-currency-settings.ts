import mysql from 'mysql2/promise';
import { v4 as uuidv4 } from 'uuid';
import { dbConfig } from '../lib/config';

async function addCurrencySettings() {
  let connection;

  try {
    console.log('Connecting to database...');

    // Create connection
    connection = await mysql.createConnection({
      host: dbConfig.host,
      user: dbConfig.username,
      password: dbConfig.password,
      database: dbConfig.database,
      port: dbConfig.port,
    });

    console.log('Adding currency settings...');

    const currencySettings = [
      {
        id: uuidv4(),
        key: 'default_currency',
        value: 'USD',
        type: 'select',
        group_name: 'currency',
        label: 'Default Currency',
        description: 'The default currency for the application',
      },
      {
        id: uuidv4(),
        key: 'currency_symbol',
        value: '$',
        type: 'text',
        group_name: 'currency',
        label: 'Currency Symbol',
        description: 'Symbol to display for the default currency',
      },
      {
        id: uuidv4(),
        key: 'currency_position',
        value: 'before',
        type: 'select',
        group_name: 'currency',
        label: 'Currency Position',
        description: 'Position of currency symbol relative to amount',
      },
      {
        id: uuidv4(),
        key: 'decimal_places',
        value: '2',
        type: 'number',
        group_name: 'currency',
        label: 'Decimal Places',
        description: 'Number of decimal places to show for currency amounts',
      },
      {
        id: uuidv4(),
        key: 'thousands_separator',
        value: ',',
        type: 'text',
        group_name: 'currency',
        label: 'Thousands Separator',
        description: 'Character to use as thousands separator',
      },
      {
        id: uuidv4(),
        key: 'decimal_separator',
        value: '.',
        type: 'text',
        group_name: 'currency',
        label: 'Decimal Separator',
        description: 'Character to use as decimal separator',
      },
      {
        id: uuidv4(),
        key: 'supported_currencies',
        value: 'USD,EUR,GBP,BDT,INR',
        type: 'text',
        group_name: 'currency',
        label: 'Supported Currencies',
        description: 'Comma-separated list of supported currency codes',
      },
      {
        id: uuidv4(),
        key: 'auto_currency_detection',
        value: 'false',
        type: 'boolean',
        group_name: 'currency',
        label: 'Auto Currency Detection',
        description: 'Automatically detect user currency based on location',
      },
      {
        id: uuidv4(),
        key: 'currency_conversion_api',
        value: '',
        type: 'text',
        group_name: 'currency',
        label: 'Currency Conversion API Key',
        description: 'API key for currency conversion service',
      },
    ];

    // Insert currency settings
    for (const setting of currencySettings) {
      await connection.query(
        `INSERT INTO site_settings (id, setting_key, value, type, group_name, label, description, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
         ON DUPLICATE KEY UPDATE
         value = VALUES(value),
         label = VALUES(label),
         description = VALUES(description),
         updated_at = NOW()`,
        [
          setting.id,
          setting.key,
          setting.value,
          setting.type,
          setting.group_name,
          setting.label,
          setting.description,
        ]
      );
    }

    console.log('Currency settings added successfully!');
  } catch (error) {
    console.error('Error adding currency settings:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
addCurrencySettings();
