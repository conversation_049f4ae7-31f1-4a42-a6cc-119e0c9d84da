import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addFanPageMessagesTable() {
  let connection;

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST || 'localhost',
      user: process.env.DATABASE_USERNAME || 'root',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || 'hifnf',
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('Connected to database');

    // Create fan_page_messages table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS fan_page_messages (
        id VARCHAR(255) PRIMARY KEY,
        fanPageId VARCHAR(255) NOT NULL,
        senderId VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        \`read\` BOOLEAN NOT NULL DEFAULT FALSE,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (fanPageId) REFERENCES fan_pages(id) ON DELETE CASCADE,
        FOREIGN KEY (senderId) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_fan_page_messages_page_id (fanPageId),
        INDEX idx_fan_page_messages_sender_id (senderId),
        INDEX idx_fan_page_messages_created_at (createdAt)
      )
    `);

    console.log('✅ fan_page_messages table created successfully');

    // Update notifications table to support fan page messages
    await connection.query(`
      ALTER TABLE notifications
      MODIFY COLUMN type ENUM(
        'like', 'comment', 'friend_request', 'friend_accept',
        'message', 'post_mention', 'comment_mention',
        'fan_page_follow', 'fan_page_post', 'fan_page_message'
      ) NOT NULL
    `);

    console.log('✅ notifications table updated to support fan page messages');

    console.log('🎉 Fan page messaging system setup completed!');

  } catch (error) {
    console.error('❌ Error setting up fan page messaging:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
addFanPageMessagesTable();
