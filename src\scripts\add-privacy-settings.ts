import mysql from 'mysql2/promise';
import { config } from 'dotenv';

// Load environment variables
config();

async function addPrivacySettingsColumns() {
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || 'localhost',
    user: process.env.DATABASE_USERNAME || 'root',
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME || 'hifnf',
    port: parseInt(process.env.DATABASE_PORT || '3306'),
  });

  try {
    console.log('Adding privacy settings columns to users table...');

    // Add privacy settings columns to users table
    const privacyColumns = [
      "profile_visibility ENUM('public', 'friends', 'private') DEFAULT 'public'",
      "show_email BOOLEAN DEFAULT FALSE",
      "show_phone BOOLEAN DEFAULT FALSE", 
      "show_birthday BOOLEAN DEFAULT TRUE",
      "show_location BOOLEAN DEFAULT TRUE",
      "allow_friend_requests ENUM('everyone', 'friends-of-friends', 'nobody') DEFAULT 'everyone'",
      "default_post_privacy ENUM('public', 'friends', 'private') DEFAULT 'public'",
      "allow_tagging BOOLEAN DEFAULT TRUE",
      "allow_messages_from ENUM('everyone', 'friends', 'nobody') DEFAULT 'everyone'",
      "show_online_status BOOLEAN DEFAULT TRUE",
      "allow_search_by_email BOOLEAN DEFAULT TRUE",
      "allow_search_by_phone BOOLEAN DEFAULT FALSE"
    ];

    for (const column of privacyColumns) {
      const columnName = column.split(' ')[0];
      
      try {
        // Check if column exists
        const [rows] = await connection.execute(
          `SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND COLUMN_NAME = ?`,
          [process.env.DATABASE_NAME || 'hifnf', columnName]
        );

        if ((rows as any[]).length === 0) {
          // Column doesn't exist, add it
          await connection.execute(`ALTER TABLE users ADD COLUMN ${column}`);
          console.log(`✅ Added column: ${columnName}`);
        } else {
          console.log(`⚠️  Column ${columnName} already exists, skipping...`);
        }
      } catch (error) {
        console.error(`❌ Error adding column ${columnName}:`, error);
      }
    }

    console.log('✅ Privacy settings columns migration completed!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await connection.end();
  }
}

// Run the migration
addPrivacySettingsColumns().catch(console.error);
