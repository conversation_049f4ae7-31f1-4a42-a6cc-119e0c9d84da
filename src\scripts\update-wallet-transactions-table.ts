import mysql from 'mysql2/promise';

async function updateWalletTransactionsTable() {
  try {
    console.log('🚀 Updating wallet_transactions table...');

    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'hifnf',
    });

    // Check if columns exist
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'wallet_transactions'
    `);

    const columnNames = (columns as any[]).map(col => col.COLUMN_NAME);
    console.log('Existing columns:', columnNames);

    // Add missing columns
    if (!columnNames.includes('fromWalletType')) {
      await connection.execute(`
        ALTER TABLE wallet_transactions 
        ADD COLUMN fromWalletType ENUM('general', 'earning') NULL
      `);
      console.log('✅ Added fromWalletType column');
    } else {
      console.log('ℹ️ fromWalletType column already exists');
    }

    if (!columnNames.includes('toWalletType')) {
      await connection.execute(`
        ALTER TABLE wallet_transactions 
        ADD COLUMN toWalletType ENUM('general', 'earning') NULL
      `);
      console.log('✅ Added toWalletType column');
    } else {
      console.log('ℹ️ toWalletType column already exists');
    }

    // Update status enum to include 'processing'
    await connection.execute(`
      ALTER TABLE wallet_transactions 
      MODIFY COLUMN status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending' NOT NULL
    `);
    console.log('✅ Updated status enum to include processing');

    // Update paymentGateway column name if it's different
    if (columnNames.includes('paymentGatewayId') && !columnNames.includes('paymentGateway')) {
      await connection.execute(`
        ALTER TABLE wallet_transactions 
        CHANGE COLUMN paymentGatewayId paymentGateway VARCHAR(100)
      `);
      console.log('✅ Renamed paymentGatewayId to paymentGateway');
    }

    // Update description to note if needed
    if (columnNames.includes('description') && !columnNames.includes('note')) {
      await connection.execute(`
        ALTER TABLE wallet_transactions 
        CHANGE COLUMN description note TEXT
      `);
      console.log('✅ Renamed description to note');
    }

    await connection.end();
    console.log('✅ Wallet transactions table updated successfully!');

  } catch (error) {
    console.error('❌ Error updating wallet transactions table:', error);
    process.exit(1);
  }
}

// Run the script
updateWalletTransactionsTable().then(() => {
  console.log('🎉 Script completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
