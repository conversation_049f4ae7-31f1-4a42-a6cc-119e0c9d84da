import mysql from "mysql2/promise";
import * as dotenv from "dotenv";

dotenv.config();

async function main() {
  console.log("Creating products table...");

  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || "localhost",
    user: process.env.DATABASE_USERNAME || "root",
    password: process.env.DATABASE_PASSWORD || "",
    database: process.env.DATABASE_NAME || "hifnf_db",
    multipleStatements: true,
  });

  try {
    // Check if stores table already exists
    const [storesTable] = await connection.execute(
      "SHOW TABLES LIKE 'stores'"
    );

    // @ts-ignore
    if (storesTable.length > 0) {
      console.log("Stores table already exists in database.");
    } else {
      console.log("Creating stores table...");

      // Create stores table
      await connection.execute(`
        CREATE TABLE stores (
          id VARCHAR(255) PRIMARY KEY,
          name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
          slug VARCHAR(100) NOT NULL UNIQUE,
          description TEXT,
          logo VARCHAR(255),
          banner VARCHAR(255),
          location VARCHAR(255),
          ownerId VARCHAR(255) NOT NULL,
          isVerified BOOLEAN DEFAULT FALSE,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      console.log("Stores table created successfully!");
    }

    // Check if products table already exists
    const [productsTable] = await connection.execute(
      "SHOW TABLES LIKE 'products'"
    );

    // @ts-ignore
    if (productsTable.length > 0) {
      console.log("Products table already exists in database.");
    } else {
      console.log("Creating products table...");

      // Create products table
      await connection.execute(`
        CREATE TABLE products (
          id VARCHAR(255) PRIMARY KEY,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          price INT NOT NULL,
          item_condition ENUM('new', 'like_new', 'good', 'fair', 'poor') NOT NULL,
          category VARCHAR(100) NOT NULL,
          location VARCHAR(255),
          photos JSON,
          storeId VARCHAR(255) NOT NULL,
          viewCount INT DEFAULT 0,
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);

      console.log("Products table created successfully!");
    }

    console.log("Tables setup complete!");
  } catch (error) {
    console.error("Error setting up tables:", error);
    process.exit(1);
  } finally {
    await connection.end();
  }
}

main().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});
