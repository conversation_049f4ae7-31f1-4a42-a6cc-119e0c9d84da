import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { Button } from "@/components/ui/Button";
import { BookmarkIcon } from "@heroicons/react/24/outline";
import { LeftSidebar } from "@/components/layout/LeftSidebar";
import { SidebarErrorBoundary } from "@/components/error/ErrorBoundary";

export default async function SavedPage() {
  await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row">
          {/* Left sidebar - 20% */}
          <div className="w-full lg:w-[20%] mb-5 lg:mb-0">
            <SidebarErrorBoundary>
              <LeftSidebar />
            </SidebarErrorBoundary>
            {/* This is an empty div that takes up the same space as the fixed sidebar */}
            <div className="hidden lg:block h-[calc(100vh-5rem)]"></div>
          </div>

          {/* Gap between left sidebar and main content - 5% */}
          <div className="hidden lg:block lg:w-[5%]"></div>

          {/* Main content - 70% */}
          <div className="w-full lg:w-[70%] space-y-5">
            <h1 className="mb-6 text-2xl font-bold text-gray-900">
              Saved Items
            </h1>

            <div className="mb-8 flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <div className="flex space-x-2">
                <Button variant="outline">All Items</Button>
                <Button variant="outline">Posts</Button>
                <Button variant="outline">Links</Button>
                <Button variant="outline">Photos</Button>
                <Button variant="outline">Videos</Button>
              </div>
            </div>

            {/* Empty state */}
            <div className="overflow-hidden rounded-lg bg-white shadow">
              <div className="p-8 text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                  <BookmarkIcon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">
                  No saved items yet
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  When you save items, they'll appear here for easy access.
                </p>
                <div className="mt-6">
                  <Button>
                    Browse News Feed
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
