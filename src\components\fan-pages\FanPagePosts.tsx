"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import {
  HeartIcon,
  ChatBubbleOvalLeftIcon,
  ShareIcon,
  EyeIcon,
  EllipsisHorizontalIcon,
  DocumentTextIcon,
  PencilIcon,
  TrashIcon,
  BookmarkIcon,
  ExclamationTriangleIcon
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid, CheckBadgeIcon as CheckBadgeIconSolid } from "@heroicons/react/24/solid";
import { formatDistanceToNow } from "date-fns";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { PostTime } from "@/components/ui/TimeDisplay";
import { EditPostModal } from "./EditPostModal";
import { FanPageCommentSection } from "./FanPageCommentSection";
import { PostCard } from "@/components/feed/PostCard";

interface FanPagePost {
  id: string;
  content: string;
  images: string[] | null;
  videos: string[] | null;
  privacy: string;
  backgroundColor: string | null;
  feeling: string | null;
  activity: string | null;
  location: string | null;
  formattedContent: boolean | null;
  type: 'fan_page_post';
  createdAt: string;
  user: null;
  fanPage: {
    id: string;
    name: string;
    username: string;
    profileImage: string | null;
    isVerified: boolean;
  };
  group: null;
  _count: {
    likes: number;
    dislikes: number;
    comments: number;
    shares: number;
  };
  liked: boolean;
  disliked: boolean;
}

interface FanPagePostsProps {
  pageId: string;
  canPost: boolean;
  onCreatePost?: () => void;
  refreshKey?: number;
  pageOwnerId?: string;
}

export function FanPagePosts({ pageId, canPost, onCreatePost, refreshKey, pageOwnerId }: FanPagePostsProps) {
  const { data: session } = useSession();
  const [posts, setPosts] = useState<FanPagePost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [editingPost, setEditingPost] = useState<FanPagePost | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showBackToTop, setShowBackToTop] = useState(false);

  // Infinity scroll refs
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const postsContainerRef = useRef<HTMLDivElement | null>(null);

  const fetchPosts = async (pageNum: number = 1, append: boolean = false) => {
    try {
      if (pageNum === 1) setLoading(true);
      else setLoadingMore(true);
      setError(null); // Clear previous errors

      const response = await fetch(`/api/fan-pages/${pageId}/posts?page=${pageNum}&limit=10`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch posts`);
      }

      const data = await response.json();

      if (!data.posts || !Array.isArray(data.posts)) {
        throw new Error('Invalid response format: posts array not found');
      }

      // Transform posts to match universal PostCard interface
      const transformedPosts = data.posts.map((post: any) => ({
        ...post,
        type: 'fan_page_post' as const,
        user: null,
        group: null,
        privacy: post.privacy || 'public',
        backgroundColor: post.backgroundColor || null,
        feeling: post.feeling || null,
        activity: post.activity || null,
        location: post.location || null,
        formattedContent: post.formattedContent || false,
        _count: {
          likes: post.likeCount || 0,
          dislikes: post.dislikeCount || 0,
          comments: post.commentCount || 0,
          shares: post.shareCount || 0
        },
        liked: post.liked || false,
        disliked: post.disliked || false
      }));

      if (append) {
        setPosts(prev => {
          // Prevent duplicate posts
          const existingIds = new Set(prev.map(p => p.id));
          const newPosts = transformedPosts.filter((post: FanPagePost) => !existingIds.has(post.id));
          return [...prev, ...newPosts];
        });
      } else {
        setPosts(transformedPosts);
      }

      setHasMore(data.pagination?.hasNextPage || false);
      setPage(pageNum);

    } catch (error) {
      console.error("Error fetching posts:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch posts");
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    if (pageId && pageId.trim() !== '') {
      fetchPosts();
    } else {
      setError('Invalid page ID');
      setLoading(false);
    }
  }, [pageId]);

  // Refresh posts when refreshKey changes
  useEffect(() => {
    if (refreshKey && refreshKey > 0) {
      fetchPosts();
    }
  }, [refreshKey]);

  const loadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchPosts(page + 1, true);
    }
  }, [loadingMore, hasMore, page]);

  // Setup intersection observer for infinity scroll
  useEffect(() => {
    if (!loadMoreRef.current) return;

    // Cleanup previous observer
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create new observer with throttling
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !loadingMore && !loading) {
          // Clear any existing timeout
          if (loadingTimeoutRef.current) {
            clearTimeout(loadingTimeoutRef.current);
          }

          // Throttle the loading to prevent rapid fire requests
          loadingTimeoutRef.current = setTimeout(() => {
            loadMore();
          }, 200); // 200ms delay to prevent too frequent requests
        }
      },
      {
        root: null,
        rootMargin: '200px', // Start loading 200px before the element comes into view
        threshold: 0.1,
      }
    );

    // Start observing
    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    // Cleanup on unmount
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [hasMore, loadingMore, loading, loadMore]);

  // Back to top functionality
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setShowBackToTop(scrollTop > 1000); // Show after scrolling 1000px
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleLike = async (postId: string) => {
    if (!session) return;

    // Store original state for rollback
    const originalPost = posts.find(p => p.id === postId);
    if (!originalPost) return;

    const originalState = {
      liked: originalPost.liked,
      disliked: originalPost.disliked,
      likeCount: originalPost._count.likes
    };

    // Optimistic update
    const newLiked = !originalPost.liked;
    setPosts(prev => prev.map(post =>
      post.id === postId
        ? {
            ...post,
            liked: newLiked,
            disliked: originalPost.disliked && newLiked ? false : post.disliked,
            _count: {
              ...post._count,
              likes: post._count.likes + (newLiked ? 1 : -1)
            }
          }
        : post
    ));

    try {
      const response = await fetch(`/api/fan-pages/posts/${postId}/like`, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Failed to like post");
      }

      const data = await response.json();
      // Update with server response
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? {
              ...post,
              _count: {
                ...post._count,
                likes: data.likeCount
              },
              liked: data.action === 'liked',
              disliked: false // Remove dislike if liked
            }
          : post
      ));
    } catch (error) {
      console.error("Error liking post:", error);

      // Rollback optimistic update
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? {
              ...post,
              liked: originalState.liked,
              disliked: originalState.disliked,
              _count: {
                ...post._count,
                likes: originalState.likeCount
              }
            }
          : post
      ));
    }
  };

  const handleDislike = async (postId: string) => {
    if (!session) return;

    // Store original state for rollback
    const originalPost = posts.find(p => p.id === postId);
    if (!originalPost) return;

    const originalState = {
      liked: originalPost.liked,
      disliked: originalPost.disliked,
      likeCount: originalPost._count.likes,
      dislikeCount: originalPost._count.dislikes || 0
    };

    // Optimistic update
    const newDisliked = !originalPost.disliked;
    setPosts(prev => prev.map(post =>
      post.id === postId
        ? {
            ...post,
            disliked: newDisliked,
            liked: originalPost.liked && newDisliked ? false : post.liked,
            _count: {
              ...post._count,
              dislikes: (post._count.dislikes || 0) + (newDisliked ? 1 : -1),
              likes: originalPost.liked && newDisliked ? post._count.likes - 1 : post._count.likes
            }
          }
        : post
    ));

    try {
      const response = await fetch(`/api/fan-pages/posts/${postId}/dislike`, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Failed to dislike post");
      }

      const data = await response.json();
      // Update with server response
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? {
              ...post,
              _count: {
                ...post._count,
                dislikes: data.dislikeCount || 0,
                likes: data.likeCount || post._count.likes
              },
              disliked: data.action === 'disliked',
              liked: false // Remove like if disliked
            }
          : post
      ));
    } catch (error) {
      console.error("Error disliking post:", error);

      // Rollback optimistic update
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? {
              ...post,
              liked: originalState.liked,
              disliked: originalState.disliked,
              _count: {
                ...post._count,
                likes: originalState.likeCount,
                dislikes: originalState.dislikeCount
              }
            }
          : post
      ));
    }
  };

  const handleEdit = (post: FanPagePost) => {
    setEditingPost(post);
    setShowEditModal(true);
  };

  const handlePostUpdated = (updatedPost: any) => {
    setPosts(prev => prev.map(post =>
      post.id === updatedPost.id ? {
        ...post,
        content: updatedPost.content,
        images: updatedPost.images,
        videos: updatedPost.videos,
        type: updatedPost.type
      } : post
    ));
    setShowEditModal(false);
    setEditingPost(null);
  };

  const handleDelete = async (postId: string) => {
    try {
      const response = await fetch(`/api/fan-pages/posts/${postId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        // Remove the post from local state
        setPosts(prev => prev.filter(post => post.id !== postId));
      } else {
        const errorData = await response.json();
        console.error("Error deleting post:", errorData.error);
      }
    } catch (error) {
      console.error("Error deleting post:", error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border p-6 animate-pulse">
            <div className="flex items-center space-x-3 mb-4">
              <div className="h-12 w-12 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-1/6"></div>
              </div>
            </div>
            <div className="space-y-2 mb-4">
              <div className="h-4 bg-gray-300 rounded w-full"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            </div>
            <div className="h-64 bg-gray-300 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6 text-center">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={() => fetchPosts()}>Try Again</Button>
      </div>
    );
  }

  return (
    <div ref={postsContainerRef} className="space-y-6">
      {/* Create Post Button */}
      {canPost && (
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <Button
            onClick={onCreatePost}
            variant="outline"
            className="w-full justify-start text-gray-500"
          >
            What's on your mind?
          </Button>
        </div>
      )}

      {/* Posts */}
      {posts.length === 0 ? (
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-12 text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <DocumentTextIcon className="h-10 w-10 text-blue-600" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-3">No posts yet</h3>
          <p className="text-gray-600 text-lg">
            {canPost ? "Be the first to share something amazing!" : "This page hasn't posted anything yet."}
          </p>
        </div>
      ) : (
        <>
          {posts.map((post) => (
            <div key={post.id} className="transform transition-all duration-300 hover:translate-y-[-2px]">
              <PostCard
                post={post}
                onLike={() => handleLike(post.id)}
                onDislike={() => handleDislike(post.id)}
              />
            </div>
          ))}

          {/* Infinity Scroll Trigger */}
          {hasMore && (
            <div
              ref={loadMoreRef}
              className="flex justify-center items-center py-8"
            >
              {loadingMore ? (
                <div className="flex items-center space-x-3 text-gray-600">
                  <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                  <span className="text-lg font-medium">Loading more posts...</span>
                </div>
              ) : (
                <div className="text-center">
                  <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-pulse mx-auto mb-2" />
                  <p className="text-gray-500 text-sm">Scroll to load more</p>
                </div>
              )}
            </div>
          )}

          {/* End of posts indicator */}
          {!hasMore && posts.length > 0 && (
            <div className="text-center py-8">
              <div className="inline-flex items-center px-4 py-2 bg-gray-100 rounded-full text-gray-600">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">You've reached the end</span>
              </div>
            </div>
          )}
        </>
      )}

      {/* Edit Post Modal */}
      {editingPost && (
        <EditPostModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setEditingPost(null);
          }}
          onPostUpdated={handlePostUpdated}
          post={{
            ...editingPost,
            likeCount: editingPost._count.likes,
            commentCount: editingPost._count.comments,
            shareCount: editingPost._count.shares,
            viewCount: 0 // Default value since it's not in _count
          }}
          pageName={editingPost.fanPage.name}
          pageImage={editingPost.fanPage.profileImage}
        />
      )}

      {/* Back to Top Button */}
      {showBackToTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 z-50 w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center"
          aria-label="Back to top"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
        </button>
      )}
    </div>
  );
}

