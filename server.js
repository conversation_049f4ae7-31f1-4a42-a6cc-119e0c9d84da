// Memory optimization settings
process.env.NODE_OPTIONS = process.env.NODE_OPTIONS || '--max-old-space-size=4096';

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3001;

// Memory monitoring in development
if (dev) {
  console.log('🔧 Development mode - Memory monitoring enabled');
  const logMemory = () => {
    const usage = process.memoryUsage();
    const formatMB = (bytes) => (bytes / 1024 / 1024).toFixed(2) + ' MB';
    console.log('📊 Memory:', {
      heap: formatMB(usage.heapUsed),
      total: formatMB(usage.heapTotal),
      rss: formatMB(usage.rss)
    });
  };

  // Log memory every 2 minutes
  setInterval(logMemory, 120000);
  logMemory(); // Initial log
}

console.log('Starting server with config:', { dev, hostname, port });

const app = next({ dev });
const handle = app.getRequestHandler();

console.log('Preparing Next.js app...');
app.prepare().then(() => {
  console.log('Next.js app prepared successfully');
  const httpServer = createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('Internal Server Error');
    }
  });

  httpServer.listen(port, (err) => {
    if (err) throw err;
    console.log(`> Ready on http://${hostname}:${port}`);
    console.log('> Next.js server started successfully');
  });
});
