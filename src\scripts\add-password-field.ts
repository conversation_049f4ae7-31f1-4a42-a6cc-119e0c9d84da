import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addPasswordField() {
  console.log('Starting migration to add password field to users table...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  try {
    // Connect to the database
    const connection = await mysql.createConnection({
      host: dbHost,
      user: dbU<PERSON>,
      password: dbPassword,
      database: dbName,
      port: dbPort,
      multipleStatements: true,
    });

    console.log(`Connected to database '${dbName}'`);
    
    // Check if password column already exists
    const [columns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'password'"
    );
    
    // @ts-ignore
    if (columns.length > 0) {
      console.log("Password column already exists in users table.");
    } else {
      console.log("Adding password column to users table...");
      
      // Add password column after email
      await connection.execute(
        "ALTER TABLE users ADD COLUMN password VARCHAR(255) AFTER email"
      );
      
      console.log("Password column added successfully!");
    }

    // Close the connection
    await connection.end();
    console.log("Migration completed successfully!");

  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  }
}

// Run the migration
addPasswordField();
