"use client";

import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/Button";
import { formatTimeAgo } from "@/lib/utils";
import {
  UsersIcon,
  ChevronLeftIcon,
  GlobeAltIcon,
  LockClosedIcon,
  PlusIcon,
  Cog6ToothIcon
} from "@heroicons/react/24/outline";

interface GroupHeaderProps {
  group: {
    id: string;
    name: string;
    description: string | null;
    visibility: "public" | "private-visible" | "private-hidden";
    coverImage: string | null;
    category: string | null;
    createdAt: Date;
  };
  membersCount: number;
  isCreator: boolean;
  isAdmin: boolean;
  isMember: boolean;
  isPending: boolean;
  isPublic: boolean;
  isPrivateVisible: boolean;
}

export function GroupHeader({
  group,
  membersCount,
  isCreator,
  isAdmin,
  isMember,
  isPending,
  isPublic,
  isPrivateVisible
}: GroupHeaderProps) {
  return (
    <div className="relative">
      {/* Cover image */}
      <div className="h-64 w-full bg-gradient-to-r from-blue-600 to-indigo-700 sm:h-80 lg:h-96">
        {group.coverImage ? (
          <Image
            src={group.coverImage}
            alt={group.name}
            fill
            className="object-cover"
            priority
          />
        ) : (
          <div className="flex h-full w-full items-center justify-center">
            <UsersIcon className="h-24 w-24 text-white opacity-20" />
          </div>
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
      </div>

      {/* Back button */}
      <div className="absolute top-4 left-4 z-10">
        <Link href="/groups">
          <Button variant="outline" size="sm" className="bg-white/80 backdrop-blur-sm hover:bg-white">
            <ChevronLeftIcon className="mr-1 h-4 w-4" />
            Back
          </Button>
        </Link>
      </div>

      {/* Group info overlay */}
      <div className="absolute bottom-0 left-0 right-0 px-4 py-6 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col items-start sm:flex-row sm:items-end sm:justify-between">
            <div className="mb-4 sm:mb-0">
              <div className="flex items-center">
                {isPublic ? (
                  <div className="mr-2 rounded-full bg-white/20 px-2 py-1 backdrop-blur-sm">
                    <div className="flex items-center">
                      <GlobeAltIcon className="h-3.5 w-3.5 text-white" />
                      <span className="ml-1 text-xs font-medium text-white">Public</span>
                    </div>
                  </div>
                ) : (
                  <div className="mr-2 rounded-full bg-white/20 px-2 py-1 backdrop-blur-sm">
                    <div className="flex items-center">
                      <LockClosedIcon className="h-3.5 w-3.5 text-white" />
                      <span className="ml-1 text-xs font-medium text-white">
                        {isPrivateVisible ? "Private (Visible)" : "Private (Hidden)"}
                      </span>
                    </div>
                  </div>
                )}
                {group.category && (
                  <div className="rounded-full bg-white/20 px-2 py-1 backdrop-blur-sm">
                    <span className="text-xs font-medium text-white">{group.category}</span>
                  </div>
                )}
              </div>
              <h1 className="mt-2 text-3xl font-bold text-white drop-shadow-sm sm:text-4xl">
                {group.name}
              </h1>
              <div className="mt-2 flex items-center text-sm text-white/90">
                <span>{membersCount} members</span>
                <span className="mx-2">•</span>
                <span>Created {formatTimeAgo(group.createdAt)}</span>
              </div>
            </div>

            <div className="flex space-x-2">
              {isCreator || isAdmin ? (
                <Button className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-transparent">
                  <Cog6ToothIcon className="mr-1 h-5 w-5" />
                  Manage Group
                </Button>
              ) : isMember ? (
                <Button variant="outline" className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-transparent">
                  <UsersIcon className="mr-1 h-5 w-5" />
                  Member
                </Button>
              ) : isPending ? (
                <Button variant="outline" className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-transparent" disabled>
                  Request Pending
                </Button>
              ) : (
                <Button className="bg-white text-blue-600 hover:bg-white/90">
                  <PlusIcon className="mr-1 h-5 w-5" />
                  {isPublic ? "Join Group" : "Request to Join"}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
