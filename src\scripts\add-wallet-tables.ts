import { db } from '../lib/db';
import {
  wallets,
  walletTransactions,
  agents,
  paymentGateways,
  walletSettings,
  pinCodes,
  cashoutRequests
} from '../lib/db/schema';
import { v4 as uuidv4 } from 'uuid';

async function addWalletTables() {
  try {
    console.log('🚀 Adding wallet system tables...');

    // Create default wallet settings
    const defaultSettings = [
      {
        id: uuidv4(),
        key: 'deposit_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: 'Enable/disable deposit functionality',
        category: 'features',
        isSystem: true,
      },
      {
        id: uuidv4(),
        key: 'send_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: 'Enable/disable money transfer functionality',
        category: 'features',
        isSystem: true,
      },
      {
        id: uuidv4(),
        key: 'cashout_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: 'Enable/disable cashout functionality',
        category: 'features',
        isSystem: true,
      },
      {
        id: uuidv4(),
        key: 'internal_transfer_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: 'Enable/disable internal wallet transfers',
        category: 'features',
        isSystem: true,
      },
      {
        id: uuidv4(),
        key: 'daily_deposit_limit',
        value: '10000.00',
        type: 'number' as const,
        description: 'Daily deposit limit per user',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'daily_send_limit',
        value: '5000.00',
        type: 'number' as const,
        description: 'Daily send limit per user',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'daily_cashout_limit',
        value: '2000.00',
        type: 'number' as const,
        description: 'Daily cashout limit per user',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'transfer_fee_percentage',
        value: '0.00',
        type: 'number' as const,
        description: 'Transfer fee percentage',
        category: 'fees',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'cashout_fee_percentage',
        value: '2.00',
        type: 'number' as const,
        description: 'Cashout fee percentage',
        category: 'fees',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'min_deposit_amount',
        value: '1.00',
        type: 'number' as const,
        description: 'Minimum deposit amount',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'max_deposit_amount',
        value: '10000.00',
        type: 'number' as const,
        description: 'Maximum deposit amount',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'min_send_amount',
        value: '1.00',
        type: 'number' as const,
        description: 'Minimum send amount',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'max_send_amount',
        value: '5000.00',
        type: 'number' as const,
        description: 'Maximum send amount',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'min_cashout_amount',
        value: '10.00',
        type: 'number' as const,
        description: 'Minimum cashout amount',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'max_cashout_amount',
        value: '2000.00',
        type: 'number' as const,
        description: 'Maximum cashout amount',
        category: 'limits',
        isSystem: false,
      },
    ];

    // Insert default settings
    await db.insert(walletSettings).values(defaultSettings);
    console.log('✅ Default wallet settings created');

    // Create default payment gateways
    const defaultGateways = [
      {
        id: uuidv4(),
        name: 'stripe',
        displayName: 'Stripe',
        type: 'stripe' as const,
        isActive: false,
        config: {
          publishableKey: '',
          secretKey: '',
          webhookSecret: '',
        },
        depositFee: '2.9',
        depositFixedFee: '0.30',
        minDeposit: '1.00',
        maxDeposit: '10000.00',
        currency: 'USD',
        sortOrder: 1,
      },
      {
        id: uuidv4(),
        name: 'paypal',
        displayName: 'PayPal',
        type: 'paypal' as const,
        isActive: false,
        config: {
          clientId: '',
          clientSecret: '',
          mode: 'sandbox', // sandbox or live
        },
        depositFee: '3.49',
        depositFixedFee: '0.49',
        minDeposit: '1.00',
        maxDeposit: '10000.00',
        currency: 'USD',
        sortOrder: 2,
      },
      {
        id: uuidv4(),
        name: 'sslcommerz',
        displayName: 'SSLCommerz',
        type: 'sslcommerz' as const,
        isActive: false,
        config: {
          storeId: '',
          storePassword: '',
          mode: 'sandbox', // sandbox or live
        },
        depositFee: '2.00',
        depositFixedFee: '0.00',
        minDeposit: '10.00',
        maxDeposit: '50000.00',
        currency: 'BDT',
        sortOrder: 3,
      },
      {
        id: uuidv4(),
        name: 'bkash',
        displayName: 'bKash',
        type: 'bkash' as const,
        isActive: false,
        config: {
          username: '',
          password: '',
          appKey: '',
          appSecret: '',
          mode: 'sandbox', // sandbox or live
        },
        depositFee: '1.85',
        depositFixedFee: '0.00',
        minDeposit: '10.00',
        maxDeposit: '25000.00',
        currency: 'BDT',
        sortOrder: 4,
      },
      {
        id: uuidv4(),
        name: 'uddoktapay',
        displayName: 'UddoktaPay',
        type: 'uddoktapay' as const,
        isActive: false,
        config: {
          apiKey: '',
          apiUrl: 'https://sandbox.uddoktapay.com/api/checkout-v2',
          storeId: '',
        },
        depositFee: '2.50',
        depositFixedFee: '0.00',
        minDeposit: '10.00',
        maxDeposit: '100000.00',
        currency: 'BDT',
        sortOrder: 5,
      },
      {
        id: uuidv4(),
        name: 'manual',
        displayName: 'Manual Payment Method',
        type: 'manual' as const,
        isActive: false,
        config: {
          instructions: 'Please contact support for manual payment instructions.',
          accountDetails: 'Bank account details will be provided upon request.',
        },
        depositFee: '0.00',
        depositFixedFee: '0.00',
        minDeposit: '1.00',
        maxDeposit: '10000.00',
        currency: 'USD',
        sortOrder: 6,
      },
    ];

    // Insert default payment gateways
    await db.insert(paymentGateways).values(defaultGateways);
    console.log('✅ Default payment gateways created');

    // Create sample agents
    const sampleAgents = [
      {
        id: uuidv4(),
        userId: 'sample-user-1', // This should be replaced with actual user IDs
        name: 'John Doe - bKash Agent',
        phone: '+*************',
        email: '<EMAIL>',
        location: 'Dhaka, Bangladesh',
        serviceType: 'bKash',
        accountNumber: '***********',
        accountName: 'John Doe',
        dailyLimit: '50000.00',
        currentDailyAmount: '0.00',
        commission: '2.00',
        isActive: true,
        isVerified: true,
        rating: '4.8',
        totalTransactions: 150,
        totalAmount: '125000.00',
      },
      {
        id: uuidv4(),
        userId: 'sample-user-2', // This should be replaced with actual user IDs
        name: 'Jane Smith - Nagad Agent',
        phone: '+*************',
        email: '<EMAIL>',
        location: 'Chittagong, Bangladesh',
        serviceType: 'Nagad',
        accountNumber: '***********',
        accountName: 'Jane Smith',
        dailyLimit: '40000.00',
        currentDailyAmount: '0.00',
        commission: '1.85',
        isActive: true,
        isVerified: true,
        rating: '4.9',
        totalTransactions: 200,
        totalAmount: '180000.00',
      },
      {
        id: uuidv4(),
        userId: 'sample-user-3', // This should be replaced with actual user IDs
        name: 'Mike Johnson - Bank Agent',
        phone: '+*************',
        email: '<EMAIL>',
        location: 'Sylhet, Bangladesh',
        serviceType: 'Bank Transfer',
        accountNumber: '**********',
        accountName: 'Mike Johnson',
        dailyLimit: '100000.00',
        currentDailyAmount: '0.00',
        commission: '1.50',
        isActive: true,
        isVerified: true,
        rating: '4.7',
        totalTransactions: 75,
        totalAmount: '95000.00',
      },
    ];

    // Insert sample agents (commented out since we need real user IDs)
    // await db.insert(agents).values(sampleAgents);
    console.log('ℹ️  Sample agents data prepared (not inserted - requires real user IDs)');

    console.log('🎉 Wallet system tables and default data added successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Configure payment gateways in the admin panel');
    console.log('2. Create agent accounts and verify them');
    console.log('3. Adjust wallet settings as needed');
    console.log('4. Test the wallet functionality');

  } catch (error) {
    console.error('❌ Error adding wallet tables:', error);
    throw error;
  }
}

// Run the script
addWalletTables()
  .then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
