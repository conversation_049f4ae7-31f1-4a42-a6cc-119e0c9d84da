import { db } from '../lib/db';

async function addMissingColumns() {
  try {
    console.log('🚀 Adding missing columns to wallet_transactions...');

    // Add fromWalletType column
    try {
      await db.execute(`
        ALTER TABLE wallet_transactions 
        ADD COLUMN fromWalletType ENUM('general', 'earning') NULL
      `);
      console.log('✅ Added fromWalletType column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ fromWalletType column already exists');
      } else {
        console.error('❌ Error adding fromWalletType:', error.message);
      }
    }

    // Add toWalletType column
    try {
      await db.execute(`
        ALTER TABLE wallet_transactions 
        ADD COLUMN toWalletType ENUM('general', 'earning') NULL
      `);
      console.log('✅ Added toWalletType column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ toWalletType column already exists');
      } else {
        console.error('❌ Error adding toWalletType:', error.message);
      }
    }

    // Update status enum to include 'processing'
    try {
      await db.execute(`
        ALTER TABLE wallet_transactions 
        MODIFY COLUMN status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending' NOT NULL
      `);
      console.log('✅ Updated status enum');
    } catch (error: any) {
      console.error('❌ Error updating status enum:', error.message);
    }

    // Add paymentGateway column if it doesn't exist
    try {
      await db.execute(`
        ALTER TABLE wallet_transactions 
        ADD COLUMN paymentGateway VARCHAR(100) NULL
      `);
      console.log('✅ Added paymentGateway column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ paymentGateway column already exists');
      } else {
        console.error('❌ Error adding paymentGateway:', error.message);
      }
    }

    // Add note column if it doesn't exist
    try {
      await db.execute(`
        ALTER TABLE wallet_transactions 
        ADD COLUMN note TEXT NULL
      `);
      console.log('✅ Added note column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ note column already exists');
      } else {
        console.error('❌ Error adding note:', error.message);
      }
    }

    console.log('✅ All missing columns added successfully!');

  } catch (error) {
    console.error('❌ Error adding missing columns:', error);
  }
}

// Run the script
addMissingColumns().then(() => {
  console.log('🎉 Script completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
