import mysql from 'mysql2/promise';
import { config } from 'dotenv';

// Load environment variables
config();

async function fixUserProfileColumns() {
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || 'localhost',
    user: process.env.DATABASE_USERNAME || 'root',
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME || 'hifnf',
    port: parseInt(process.env.DATABASE_PORT || '3306'),
  });

  try {
    console.log('Checking and adding missing user profile columns...');

    // Get current columns in users table
    const [currentColumns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'
    `, [process.env.DATABASE_NAME || 'hifnf']);

    const existingColumns = (currentColumns as any[]).map(row => row.COLUMN_NAME);
    console.log('Existing columns:', existingColumns);

    // Define all required columns
    const requiredColumns = [
      { name: 'password', definition: 'password VARCHAR(255)' },
      { name: 'phone', definition: 'phone VARCHAR(20)' },
      { name: 'role', definition: 'role VARCHAR(50) DEFAULT "user"' },
      { name: 'is_admin', definition: 'is_admin BOOLEAN DEFAULT FALSE' },
      { name: 'admin_role_id', definition: 'admin_role_id VARCHAR(255)' },
      { name: 'work', definition: 'work VARCHAR(255)' },
      { name: 'education', definition: 'education VARCHAR(255)' },
      { name: 'website', definition: 'website VARCHAR(255)' },
      { name: 'facebook', definition: 'facebook VARCHAR(255)' },
      { name: 'twitter', definition: 'twitter VARCHAR(255)' },
      { name: 'instagram', definition: 'instagram VARCHAR(255)' },
      { name: 'linkedin', definition: 'linkedin VARCHAR(255)' },
      { name: 'youtube', definition: 'youtube VARCHAR(255)' },
      { name: 'profile_visibility', definition: 'profile_visibility ENUM("public", "subscribers", "private") DEFAULT "public"' },
      { name: 'show_email', definition: 'show_email BOOLEAN DEFAULT FALSE' },
      { name: 'show_phone', definition: 'show_phone BOOLEAN DEFAULT FALSE' },
      { name: 'show_birthday', definition: 'show_birthday BOOLEAN DEFAULT TRUE' },
      { name: 'show_location', definition: 'show_location BOOLEAN DEFAULT TRUE' },
      { name: 'allow_friend_requests', definition: 'allow_friend_requests ENUM("everyone", "friends-of-friends", "nobody") DEFAULT "everyone"' },
      { name: 'default_post_privacy', definition: 'default_post_privacy ENUM("public", "subscribers", "private") DEFAULT "public"' },
      { name: 'allow_tagging', definition: 'allow_tagging BOOLEAN DEFAULT TRUE' },
      { name: 'allow_messages_from', definition: 'allow_messages_from ENUM("everyone", "subscribers", "nobody") DEFAULT "everyone"' },
      { name: 'show_online_status', definition: 'show_online_status BOOLEAN DEFAULT TRUE' },
      { name: 'allow_search_by_email', definition: 'allow_search_by_email BOOLEAN DEFAULT TRUE' },
      { name: 'allow_search_by_phone', definition: 'allow_search_by_phone BOOLEAN DEFAULT FALSE' },
      { name: 'status', definition: 'status ENUM("active", "disabled", "suspended", "deleted") DEFAULT "active" NOT NULL' },
      { name: 'is_active', definition: 'is_active BOOLEAN DEFAULT TRUE NOT NULL' },
      { name: 'suspended_at', definition: 'suspended_at TIMESTAMP NULL' },
      { name: 'suspended_reason', definition: 'suspended_reason TEXT' },
      { name: 'suspended_by', definition: 'suspended_by VARCHAR(255)' },
      { name: 'deleted_at', definition: 'deleted_at TIMESTAMP NULL' },
      { name: 'deleted_reason', definition: 'deleted_reason TEXT' },
      { name: 'deleted_by', definition: 'deleted_by VARCHAR(255)' },
    ];

    // Add missing columns
    for (const column of requiredColumns) {
      if (!existingColumns.includes(column.name)) {
        try {
          await connection.execute(`ALTER TABLE users ADD COLUMN ${column.definition}`);
          console.log(`✅ Added column: ${column.name}`);
        } catch (error: any) {
          console.error(`❌ Error adding column ${column.name}:`, error.message);
        }
      } else {
        console.log(`⚠️  Column ${column.name} already exists, skipping...`);
      }
    }

    console.log('✅ User profile columns migration completed!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await connection.end();
  }
}

// Run the migration
fixUserProfileColumns()
  .then(() => {
    console.log("Migration completed successfully!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Migration failed:", error);
    process.exit(1);
  });
