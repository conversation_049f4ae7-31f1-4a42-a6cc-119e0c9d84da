import { createConnection } from 'mysql2/promise';
import { v4 as uuidv4 } from 'uuid';
import { dbConfig } from '../lib/config';

async function addOAuthSettings() {
  console.log('Adding OAuth settings to database...');

  const connection = await createConnection({
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
  });

  try {
    // Check if OAuth settings already exist
    const [existingSettings] = await connection.execute(
      "SELECT * FROM site_settings WHERE group_name = 'oauth'"
    );

    if (Array.isArray(existingSettings) && existingSettings.length > 0) {
      console.log('OAuth settings already exist in database.');
      return;
    }

    console.log('Creating OAuth settings...');

    // OAuth settings to add
    const oauthSettings = [
      {
        id: uuidv4(),
        key: 'google_client_id',
        value: '',
        type: 'text',
        group_name: 'oauth',
        label: 'Google Client ID',
        description: 'Google OAuth 2.0 Client ID for authentication'
      },
      {
        id: uuidv4(),
        key: 'google_client_secret',
        value: '',
        type: 'password',
        group_name: 'oauth',
        label: 'Google Client Secret',
        description: 'Google OAuth 2.0 Client Secret for authentication'
      },
      {
        id: uuidv4(),
        key: 'google_oauth_enabled',
        value: 'false',
        type: 'boolean',
        group_name: 'oauth',
        label: 'Enable Google OAuth',
        description: 'Enable or disable Google OAuth login'
      },
      {
        id: uuidv4(),
        key: 'github_client_id',
        value: '',
        type: 'text',
        group_name: 'oauth',
        label: 'GitHub Client ID',
        description: 'GitHub OAuth Client ID for authentication'
      },
      {
        id: uuidv4(),
        key: 'github_client_secret',
        value: '',
        type: 'password',
        group_name: 'oauth',
        label: 'GitHub Client Secret',
        description: 'GitHub OAuth Client Secret for authentication'
      },
      {
        id: uuidv4(),
        key: 'github_oauth_enabled',
        value: 'false',
        type: 'boolean',
        group_name: 'oauth',
        label: 'Enable GitHub OAuth',
        description: 'Enable or disable GitHub OAuth login'
      },
    ];

    // Insert OAuth settings
    for (const setting of oauthSettings) {
      await connection.execute(
        `INSERT INTO site_settings (id, setting_key, value, type, group_name, label, description, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          setting.id,
          setting.key,
          setting.value,
          setting.type,
          setting.group_name,
          setting.label,
          setting.description,
        ]
      );
    }

    console.log('OAuth settings added successfully!');
  } catch (error) {
    console.error('Error adding OAuth settings:', error);
    throw error;
  } finally {
    await connection.end();
    console.log('Database connection closed.');
  }
}

// Run the script
addOAuthSettings().catch(console.error);
