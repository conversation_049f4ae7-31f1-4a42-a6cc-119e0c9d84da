import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function migrateLikesTable() {
  console.log('Starting likes table migration...');
  
  // Create a MySQL connection
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || 'localhost',
    user: process.env.DATABASE_USERNAME || 'root',
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME || 'hifnf_db',
    port: parseInt(process.env.DATABASE_PORT || '3306'),
  });

  try {
    // Check if the type column already exists
    const [columns] = await connection.execute(`
      SHOW COLUMNS FROM likes LIKE 'type'
    `);

    // If the column doesn't exist, add it
    if (Array.isArray(columns) && columns.length === 0) {
      console.log('Adding type column to likes table...');
      
      // Add the type column with a default value of 'like'
      await connection.execute(`
        ALTER TABLE likes 
        ADD COLUMN type ENUM('like', 'dislike') NOT NULL DEFAULT 'like'
      `);
      
      console.log('Type column added successfully.');
    } else {
      console.log('Type column already exists in likes table.');
    }

    console.log('Migration completed successfully.');
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await connection.end();
  }
}

// Run the migration
migrateLikesTable()
  .then(() => {
    console.log('Migration script completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
