"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import {
  UserIcon,
  ChatBubbleLeftRightIcon,
  VideoCameraIcon,
  EllipsisHorizontalIcon,
  MagnifyingGlassIcon
} from "@heroicons/react/24/outline";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { useTabVisibilityCache } from "@/hooks/useTabVisibilityCache";
import { toast } from "react-hot-toast";

interface Contact {
  id: string;
  name: string;
  username: string;
  image: string | null;
  onlineStatus: 'online' | 'away' | 'offline';
  lastSeenText: string;
  lastSeen: string | null;
  showOnlineStatus: boolean;
  createdAt: string;
}

interface ContactsResponse {
  contacts: Contact[];
  stats: {
    total: number;
    online: number;
    offline: number;
  };
  pagination: {
    limit: number;
    hasMore: boolean;
  };
}

export function Contacts() {
  const { data: session } = useSession();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({ total: 0, online: 0, offline: 0 });

  // API fetch function
  const fetchContactsFromAPI = useCallback(async () => {
    const response = await fetch('/api/friends/contacts?limit=6');
    if (!response.ok) {
      throw new Error('Failed to fetch contacts');
    }
    const data: ContactsResponse = await response.json();
    return { contacts: data.contacts || [], stats: data.stats };
  }, []);

  // Use tab visibility cache for contacts data
  const { fetchWithCache } = useTabVisibilityCache({
    cacheKey: `contacts-${session?.user?.id || 'anonymous'}`,
    fetchFunction: fetchContactsFromAPI,
    cacheDuration: 10 * 60 * 1000, // 10 minutes
    dependencies: [session?.user?.id],
    enabled: !!session?.user
  });

  const fetchContacts = useCallback(async () => {
    try {
      setLoading(true);
      const data = await fetchWithCache();
      if (data) {
        setContacts(data.contacts || []);
        setStats(data.stats || { total: 0, online: 0, offline: 0 });
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
      // Don't show error toast for contacts as it's not critical
    } finally {
      setLoading(false);
    }
  }, [fetchWithCache]);

  useEffect(() => {
    if (session?.user) {
      fetchContacts();

      // Update status every 30 seconds
      const interval = setInterval(() => {
        updateUserStatus();
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [session?.user, fetchContacts]);

  const updateUserStatus = async () => {
    try {
      await fetch('/api/users/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'online' }),
      });
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleStartChat = (contact: Contact) => {
    // Navigate to messages page with the contact
    window.location.href = `/messages/${contact.username}`;
  };

  const handleVideoCall = (contact: Contact) => {
    // For now, show a toast. In future, implement video call
    toast.success(`Video call feature coming soon for ${contact.name}!`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'offline':
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'away':
        return 'Away';
      case 'offline':
      default:
        return 'Offline';
    }
  };

  if (loading) {
    return (
      <div className="rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-base font-semibold text-gray-900">
            Contacts
          </h2>
        </div>
        <div className="space-y-2.5">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center p-1.5 animate-pulse">
              <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
              <div className="ml-3 flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (contacts.length === 0) {
    return (
      <div className="rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-base font-semibold text-gray-900">
            Contacts
          </h2>
        </div>
        <div className="text-center py-4">
          <UserIcon className="h-8 w-8 text-gray-300 mx-auto mb-2" />
          <p className="text-sm text-gray-500">No contacts yet</p>
          <Link
            href="/friends/suggestions"
            className="text-xs text-blue-500 hover:text-blue-600"
          >
            Find friends
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-xl bg-white p-4 shadow-sm hover:shadow-md transition-all duration-300">
      <div className="flex items-center justify-between mb-3">
        <div>
          <h2 className="text-base font-semibold text-gray-900">
            Contacts
          </h2>
          <p className="text-xs text-gray-500">
            {stats.online} online • {stats.total} total
          </p>
        </div>
        <div className="flex space-x-1">
          <Link
            href="/friends/contacts"
            className="rounded-full p-1.5 text-gray-400 hover:bg-gray-100 transition-colors duration-200"
            title="Search Contacts"
          >
            <MagnifyingGlassIcon className="h-4 w-4" />
          </Link>
          <button
            className="rounded-full p-1.5 text-gray-400 hover:bg-gray-100 transition-colors duration-200"
            title="New Message"
            onClick={() => toast('Select a contact to start messaging', { icon: 'ℹ️' })}
          >
            <ChatBubbleLeftRightIcon className="h-4 w-4" />
          </button>
          <button
            className="rounded-full p-1.5 text-gray-400 hover:bg-gray-100 transition-colors duration-200"
            title="Video Call"
            onClick={() => toast('Video call feature coming soon!', { icon: 'ℹ️' })}
          >
            <VideoCameraIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      <div className="space-y-2 overflow-y-auto max-h-64 pr-1">
        {contacts.slice(0, 6).map((contact) => (
          <div
            key={contact.id}
            className="flex items-center p-1.5 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer group"
            onClick={() => handleStartChat(contact)}
          >
            <div className="relative">
              <div className="h-8 w-8 flex-shrink-0 rounded-full bg-gradient-to-r from-blue-100 to-indigo-100 flex items-center justify-center shadow-sm">
                {contact.image ? (
                  <OptimizedImage
                    src={contact.image}
                    alt={contact.name}
                    width={32}
                    height={32}
                    className="rounded-full object-cover"
                  />
                ) : (
                  <UserIcon className="h-5 w-5 text-gray-500" />
                )}
              </div>
              {contact.showOnlineStatus && (
                <span
                  className={`absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full ring-1 ring-white ${getStatusColor(contact.onlineStatus)}`}
                  title={getStatusText(contact.onlineStatus)}
                ></span>
              )}
            </div>

            <div className="ml-3 flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {contact.name}
                </p>
                <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleStartChat(contact);
                    }}
                    className="p-1 rounded-full hover:bg-blue-100 text-blue-500"
                    title="Send Message"
                  >
                    <ChatBubbleLeftRightIcon className="h-3 w-3" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleVideoCall(contact);
                    }}
                    className="p-1 rounded-full hover:bg-green-100 text-green-500"
                    title="Video Call"
                  >
                    <VideoCameraIcon className="h-3 w-3" />
                  </button>
                </div>
              </div>
              <p className="text-xs text-gray-500 truncate">
                {contact.showOnlineStatus ? contact.lastSeenText : 'Status hidden'}
              </p>
            </div>
          </div>
        ))}
      </div>

      {contacts.length > 6 && (
        <div className="mt-3 pt-3 border-t border-gray-100">
          <Link
            href="/friends/contacts"
            className="flex items-center justify-center w-full px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200"
          >
            <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
            See All Contacts ({stats.total})
          </Link>
        </div>
      )}
    </div>
  );
}
