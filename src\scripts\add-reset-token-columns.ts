import mysql from "mysql2/promise";

async function addResetTokenColumns() {
  const dbHost = process.env.DB_HOST || "localhost";
  const dbUser = process.env.DB_USER || "root";
  const dbPassword = process.env.DB_PASSWORD || "";
  const dbName = process.env.DB_NAME || "hifnf";
  const dbPort = parseInt(process.env.DB_PORT || "3306");

  try {
    // Connect to the database
    const connection = await mysql.createConnection({
      host: dbHost,
      user: dbUser,
      password: dbPassword,
      database: dbName,
      port: dbPort,
      multipleStatements: true,
    });

    console.log(`Connected to database '${dbName}'`);
    
    // Add resetToken column
    try {
      await connection.execute(`
        ALTER TABLE users 
        ADD COLUMN resetToken VARCHAR(255) NULL AFTER password
      `);
      console.log('✅ Added resetToken column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ resetToken column already exists');
      } else {
        console.error('❌ Error adding resetToken column:', error.message);
      }
    }

    // Add resetTokenExpiry column
    try {
      await connection.execute(`
        ALTER TABLE users 
        ADD COLUMN resetTokenExpiry TIMESTAMP NULL AFTER resetToken
      `);
      console.log('✅ Added resetTokenExpiry column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ resetTokenExpiry column already exists');
      } else {
        console.error('❌ Error adding resetTokenExpiry column:', error.message);
      }
    }

    await connection.end();
    console.log("✅ Reset token columns migration completed!");
  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  }
}

// Run the migration
addResetTokenColumns()
  .then(() => {
    console.log("Migration completed successfully!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Migration failed:", error);
    process.exit(1);
  });
