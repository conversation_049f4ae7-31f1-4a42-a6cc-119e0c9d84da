import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { or, like, and, ne } from "drizzle-orm";

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const url = new URL(req.url);
    const query = url.searchParams.get("q");

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        data: [],
      });
    }

    // Search for users by username, email, name, or phone
    // Exclude the current user from results
    const searchResults = await db.query.users.findMany({
      where: and(
        ne(users.id, session.user.id),
        or(
          like(users.username, `%${query}%`),
          like(users.email, `%${query}%`),
          like(users.name, `%${query}%`),
          like(users.phone, `%${query}%`)
        )
      ),
      columns: {
        id: true,
        name: true,
        username: true,
        email: true,
        image: true,
      },
      limit: 10,
    });

    return NextResponse.json({
      success: true,
      data: searchResults,
    });
  } catch (error: any) {
    console.error("Error searching users:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to search users"
      },
      { status: 500 }
    );
  }
}
