import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addSubscriptionSystem() {
  let connection: mysql.Connection | null = null;

  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST || 'localhost',
      user: process.env.DATABASE_USERNAME || 'root',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || 'hifnf',
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('Connected to database');

    // Create subscriptions table
    console.log('Creating subscriptions table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS subscriptions (
        id VARCHAR(255) PRIMARY KEY,
        subscriberId VARCHAR(255) NOT NULL,
        targetUserId VARCHAR(255) NOT NULL,
        createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_subscription (subscriberId, targetUserId),
        FOREIGN KEY (subscriberId) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (targetUserId) REFERENCES users(id) ON DELETE CASCADE
      )
    `);
    console.log('Subscriptions table created successfully');

    // Check current notification types
    const [rows] = await connection.execute(`
      SHOW COLUMNS FROM notifications LIKE 'type'
    `) as any;

    if (rows.length > 0) {
      const typeEnum = rows[0].Type;
      console.log('Current notification types:', typeEnum);

      // Add subscription notification types if not already present
      if (!typeEnum.includes('subscription') || !typeEnum.includes('subscription_back')) {
        console.log('Adding subscription notification types...');

        try {
          await connection.execute(`
            ALTER TABLE notifications
            MODIFY COLUMN type ENUM(
              'like', 'comment', 'friend_request', 'friend_accept', 'message',
              'group_invite', 'group_join_request', 'group_join_approved', 'group_post', 'group_announcement',
              'event_invite', 'event_reminder', 'event_update', 'event_comment',
              'store_follow', 'store_review', 'product_new', 'product_report',
              'fan_page_follow', 'fan_page_post', 'fan_page_comment', 'fan_page_like', 'fan_page_role_added',
              'subscription', 'subscription_back'
            ) NOT NULL
          `);
          console.log('Updated notification types enum');
        } catch (error) {
          console.error('Error updating notification types enum:', error);
        }
      }

      // Add subscriptionId column to notifications table if not exists
      try {
        await connection.execute(`
          ALTER TABLE notifications
          ADD COLUMN subscriptionId VARCHAR(255) NULL
        `);
        console.log('Added subscriptionId column to notifications table');
      } catch (error) {
        // Column might already exist
        console.log('subscriptionId column might already exist');
      }
    }

    console.log('Subscription system setup completed successfully!');

  } catch (error) {
    console.error('Error setting up subscription system:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the script
if (require.main === module) {
  addSubscriptionSystem()
    .then(() => {
      console.log('Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

export { addSubscriptionSystem };
