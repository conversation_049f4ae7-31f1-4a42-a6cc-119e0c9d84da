import { MainLayout } from "@/components/layout/MainLayout";
import { requireAuth } from "@/lib/utils/auth";
import { But<PERSON> } from "@/components/ui/Button";
import { PhotoIcon, PlusIcon } from "@heroicons/react/24/outline";

export default async function PhotosPage() {
  await requireAuth();

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            Photos
          </h1>
          <Button>
            <PlusIcon className="h-5 w-5 mr-1" />
            Add Photos
          </Button>
        </div>

        <div className="mb-8 flex space-x-2 overflow-x-auto pb-2">
          <Button variant="outline">Your Photos</Button>
          <Button variant="outline">Albums</Button>
          <Button variant="outline">Tagged</Button>
        </div>

        {/* Your photos section */}
        <div className="mb-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Your Photos
          </h2>
          
          {/* Empty state */}
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="p-8 text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                <PhotoIcon className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                No photos yet
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                Photos you upload or are tagged in will appear here.
              </p>
              <div className="mt-6">
                <Button>
                  Upload Photos
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Albums section */}
        <div>
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Albums
          </h2>
          
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {/* Create album card */}
            <div className="overflow-hidden rounded-lg bg-white shadow hover:shadow-md transition-all duration-300">
              <div className="aspect-square flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg m-4">
                <div className="text-center p-4">
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                    <PlusIcon className="h-6 w-6 text-gray-600" />
                  </div>
                  <p className="mt-2 text-sm font-medium text-gray-900">
                    Create Album
                  </p>
                </div>
              </div>
            </div>
            
            {/* Profile Photos album */}
            <div className="overflow-hidden rounded-lg bg-white shadow hover:shadow-md transition-all duration-300">
              <div className="aspect-square bg-gray-100 rounded-lg m-4 flex items-center justify-center">
                <PhotoIcon className="h-12 w-12 text-gray-400" />
              </div>
              <div className="px-4 pb-4">
                <h3 className="text-sm font-medium text-gray-900">Profile Photos</h3>
                <p className="text-xs text-gray-500">0 photos</p>
              </div>
            </div>
            
            {/* Cover Photos album */}
            <div className="overflow-hidden rounded-lg bg-white shadow hover:shadow-md transition-all duration-300">
              <div className="aspect-square bg-gray-100 rounded-lg m-4 flex items-center justify-center">
                <PhotoIcon className="h-12 w-12 text-gray-400" />
              </div>
              <div className="px-4 pb-4">
                <h3 className="text-sm font-medium text-gray-900">Cover Photos</h3>
                <p className="text-xs text-gray-500">0 photos</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
