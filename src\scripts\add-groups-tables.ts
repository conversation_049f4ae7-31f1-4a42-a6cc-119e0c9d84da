import mysql from "mysql2/promise";
import * as dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";

dotenv.config();

async function main() {
  console.log("Setting up Groups feature tables...");

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Connect to the database
  const connection = await mysql.createConnection({
    host: dbHost,
    user: db<PERSON><PERSON>,
    password: dbPassword,
    database: dbName,
    port: dbPort,
    multipleStatements: true,
  });

  console.log(`Connected to database '${dbName}'`);

  try {
    // Check if groups table already exists
    const [groupsTable] = await connection.execute(
      "SHOW TABLES LIKE 'groups'"
    );

    // @ts-ignore
    if (groupsTable.length > 0) {
      console.log("Groups table already exists in database.");
    } else {
      console.log("Creating groups table...");

      // Create groups table
      await connection.execute(`
        CREATE TABLE \`groups\` (
          id VARCHAR(255) PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          visibility ENUM('public', 'private-visible', 'private-hidden') NOT NULL DEFAULT 'public',
          coverImage VARCHAR(255),
          category VARCHAR(100),
          rules TEXT,
          creatorId VARCHAR(255) NOT NULL,
          postPermission ENUM('all-members', 'admin-only') NOT NULL DEFAULT 'all-members',
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (creatorId) REFERENCES users(id) ON DELETE CASCADE
        )
      `);

      console.log("Groups table created successfully!");
    }

    // Check if group_members table already exists
    const [groupMembersTable] = await connection.execute(
      "SHOW TABLES LIKE 'group_members'"
    );

    // @ts-ignore
    if (groupMembersTable.length > 0) {
      console.log("Group members table already exists in database.");
    } else {
      console.log("Creating group_members table...");

      // Check users table structure
      const [usersColumns] = await connection.execute(
        "SHOW COLUMNS FROM users"
      );
      console.log("Users table structure:", usersColumns);

      // Create group_members table without foreign key constraints initially
      await connection.execute(`
        CREATE TABLE group_members (
          id VARCHAR(255) PRIMARY KEY,
          groupId VARCHAR(255) NOT NULL,
          userId VARCHAR(255) NOT NULL,
          role ENUM('admin', 'moderator', 'member', 'pending') NOT NULL DEFAULT 'member',
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX (groupId),
          INDEX (userId),
          UNIQUE KEY unique_group_user (groupId, userId)
        )
      `);

      // Try to add foreign key constraints separately
      try {
        await connection.execute(`
          ALTER TABLE group_members
          ADD CONSTRAINT fk_group_members_group
          FOREIGN KEY (groupId) REFERENCES \`groups\`(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for groupId");
      } catch (error) {
        console.error("Error adding groupId foreign key:", error);
      }

      try {
        await connection.execute(`
          ALTER TABLE group_members
          ADD CONSTRAINT fk_group_members_user
          FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for userId");
      } catch (error) {
        console.error("Error adding userId foreign key:", error);
      }

      console.log("Group members table created successfully!");
    }

    // Check if posts table has groupId column
    const [postsColumns] = await connection.execute(
      "SHOW COLUMNS FROM posts LIKE 'groupId'"
    );

    // @ts-ignore
    if (postsColumns.length > 0) {
      console.log("groupId column already exists in posts table.");
    } else {
      console.log("Adding groupId column to posts table...");

      // Add groupId column to posts table without foreign key constraint initially
      await connection.execute(`
        ALTER TABLE posts
        ADD COLUMN groupId VARCHAR(255) AFTER pageId
      `);

      // Try to add foreign key constraint separately
      try {
        await connection.execute(`
          ALTER TABLE posts
          ADD CONSTRAINT fk_posts_group
          FOREIGN KEY (groupId) REFERENCES \`groups\`(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for groupId in posts table");
      } catch (error) {
        console.error("Error adding groupId foreign key to posts table:", error);
      }

      console.log("groupId column added to posts table successfully!");
    }

    // Check if group_reports table already exists
    const [groupReportsTable] = await connection.execute(
      "SHOW TABLES LIKE 'group_reports'"
    );

    // @ts-ignore
    if (groupReportsTable.length > 0) {
      console.log("Group reports table already exists in database.");
    } else {
      console.log("Creating group_reports table...");

      // Create group_reports table without foreign key constraints initially
      await connection.execute(`
        CREATE TABLE group_reports (
          id VARCHAR(255) PRIMARY KEY,
          groupId VARCHAR(255) NOT NULL,
          reporterId VARCHAR(255) NOT NULL,
          reportedUserId VARCHAR(255),
          postId VARCHAR(255),
          reason ENUM('spam', 'harassment', 'inappropriate_content', 'violation', 'other') NOT NULL,
          description TEXT,
          status ENUM('pending', 'reviewed') NOT NULL DEFAULT 'pending',
          createdAt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          INDEX (groupId),
          INDEX (reporterId),
          INDEX (reportedUserId),
          INDEX (postId)
        )
      `);

      // Try to add foreign key constraints separately
      try {
        await connection.execute(`
          ALTER TABLE group_reports
          ADD CONSTRAINT fk_group_reports_group
          FOREIGN KEY (groupId) REFERENCES \`groups\`(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for groupId in group_reports table");
      } catch (error) {
        console.error("Error adding groupId foreign key to group_reports table:", error);
      }

      try {
        await connection.execute(`
          ALTER TABLE group_reports
          ADD CONSTRAINT fk_group_reports_reporter
          FOREIGN KEY (reporterId) REFERENCES users(id) ON DELETE CASCADE
        `);
        console.log("Added foreign key constraint for reporterId in group_reports table");
      } catch (error) {
        console.error("Error adding reporterId foreign key to group_reports table:", error);
      }

      try {
        await connection.execute(`
          ALTER TABLE group_reports
          ADD CONSTRAINT fk_group_reports_reported_user
          FOREIGN KEY (reportedUserId) REFERENCES users(id) ON DELETE SET NULL
        `);
        console.log("Added foreign key constraint for reportedUserId in group_reports table");
      } catch (error) {
        console.error("Error adding reportedUserId foreign key to group_reports table:", error);
      }

      try {
        await connection.execute(`
          ALTER TABLE group_reports
          ADD CONSTRAINT fk_group_reports_post
          FOREIGN KEY (postId) REFERENCES posts(id) ON DELETE SET NULL
        `);
        console.log("Added foreign key constraint for postId in group_reports table");
      } catch (error) {
        console.error("Error adding postId foreign key to group_reports table:", error);
      }

      console.log("Group reports table created successfully!");
    }

    // Update notifications table to include group-related notifications
    const [notificationTypes] = await connection.execute(
      "SHOW COLUMNS FROM notifications LIKE 'type'"
    );

    // @ts-ignore
    const typeInfo = notificationTypes[0];
    const typeEnum = typeInfo.Type;

    if (!typeEnum.includes('group_invite') || !typeEnum.includes('group_join_request')) {
      console.log("Updating notifications table to include group notification types...");

      // Update the ENUM type to include group-related notification types
      try {
        await connection.execute(`
          ALTER TABLE notifications
          MODIFY COLUMN type ENUM('like', 'comment', 'friend_request', 'friend_accept', 'message', 'group_invite', 'group_join_request', 'group_join_approved', 'group_post', 'group_announcement') NOT NULL
        `);
        console.log("Updated notification types enum");
      } catch (error) {
        console.error("Error updating notification types enum:", error);
      }

      // Check if groupId column already exists
      const [groupIdColumn] = await connection.execute(
        "SHOW COLUMNS FROM notifications LIKE 'groupId'"
      );

      // @ts-ignore
      if (groupIdColumn.length === 0) {
        // Add groupId column to notifications table without foreign key constraint initially
        try {
          await connection.execute(`
            ALTER TABLE notifications
            ADD COLUMN groupId VARCHAR(255)
          `);
          console.log("Added groupId column to notifications table");

          // Try to add foreign key constraint separately
          try {
            await connection.execute(`
              ALTER TABLE notifications
              ADD CONSTRAINT fk_notifications_group
              FOREIGN KEY (groupId) REFERENCES \`groups\`(id) ON DELETE CASCADE
            `);
            console.log("Added foreign key constraint for groupId in notifications table");
          } catch (error) {
            console.error("Error adding groupId foreign key to notifications table:", error);
          }
        } catch (error) {
          console.error("Error adding groupId column to notifications table:", error);
        }
      } else {
        console.log("groupId column already exists in notifications table");
      }

      console.log("Notifications table updated successfully!");
    } else {
      console.log("Notifications table already includes group notification types.");
    }

    console.log("Groups feature tables setup complete!");
  } catch (error) {
    console.error("Error setting up Groups feature tables:", error);
  } finally {
    await connection.end();
  }
}

main().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});
