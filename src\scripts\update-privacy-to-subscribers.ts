import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function updatePrivacyToSubscribers() {
  let connection: mysql.Connection | null = null;

  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DATABASE_HOST || 'localhost',
      user: process.env.DATABASE_USERNAME || 'root',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || 'hifnf',
      port: parseInt(process.env.DATABASE_PORT || '3306'),
    });

    console.log('Connected to database');

    // Update posts table privacy enum
    console.log('Updating posts table privacy enum...');
    try {
      await connection.execute(`
        ALTER TABLE posts
        MODIFY COLUMN privacy ENUM('public', 'subscribers', 'private') NOT NULL DEFAULT 'public'
      `);
      console.log('Updated posts table privacy enum successfully');
    } catch (error) {
      console.error('Error updating posts table privacy enum:', error);
    }

    // Update existing 'friends' privacy to 'subscribers' in posts
    console.log('Updating existing posts with friends privacy to subscribers...');
    try {
      const [result] = await connection.execute(`
        UPDATE posts SET privacy = 'subscribers' WHERE privacy = 'friends'
      `) as any;
      console.log(`Updated ${result.affectedRows} posts from 'friends' to 'subscribers' privacy`);
    } catch (error) {
      console.error('Error updating existing posts privacy:', error);
    }

    // Update users table default_post_privacy enum
    console.log('Updating users table default_post_privacy enum...');
    try {
      await connection.execute(`
        ALTER TABLE users
        MODIFY COLUMN default_post_privacy ENUM('public', 'subscribers', 'private') DEFAULT 'public'
      `);
      console.log('Updated users table default_post_privacy enum successfully');
    } catch (error) {
      console.error('Error updating users table default_post_privacy enum:', error);
    }

    // Update existing 'friends' default_post_privacy to 'subscribers' in users
    console.log('Updating existing users with friends default_post_privacy to subscribers...');
    try {
      const [result] = await connection.execute(`
        UPDATE users SET default_post_privacy = 'subscribers' WHERE default_post_privacy = 'friends'
      `) as any;
      console.log(`Updated ${result.affectedRows} users from 'friends' to 'subscribers' default_post_privacy`);
    } catch (error) {
      console.error('Error updating existing users default_post_privacy:', error);
    }

    // Update users table profile_visibility enum
    console.log('Updating users table profile_visibility enum...');
    try {
      await connection.execute(`
        ALTER TABLE users
        MODIFY COLUMN profile_visibility ENUM('public', 'subscribers', 'private') DEFAULT 'public'
      `);
      console.log('Updated users table profile_visibility enum successfully');
    } catch (error) {
      console.error('Error updating users table profile_visibility enum:', error);
    }

    // Update existing 'friends' profile_visibility to 'subscribers' in users
    console.log('Updating existing users with friends profile_visibility to subscribers...');
    try {
      const [result] = await connection.execute(`
        UPDATE users SET profile_visibility = 'subscribers' WHERE profile_visibility = 'friends'
      `) as any;
      console.log(`Updated ${result.affectedRows} users from 'friends' to 'subscribers' profile_visibility`);
    } catch (error) {
      console.error('Error updating existing users profile_visibility:', error);
    }

    // Update users table allow_messages_from enum
    console.log('Updating users table allow_messages_from enum...');
    try {
      await connection.execute(`
        ALTER TABLE users
        MODIFY COLUMN allow_messages_from ENUM('everyone', 'subscribers', 'nobody') DEFAULT 'everyone'
      `);
      console.log('Updated users table allow_messages_from enum successfully');
    } catch (error) {
      console.error('Error updating users table allow_messages_from enum:', error);
    }

    // Update existing 'friends' allow_messages_from to 'subscribers' in users
    console.log('Updating existing users with friends allow_messages_from to subscribers...');
    try {
      const [result] = await connection.execute(`
        UPDATE users SET allow_messages_from = 'subscribers' WHERE allow_messages_from = 'friends'
      `) as any;
      console.log(`Updated ${result.affectedRows} users from 'friends' to 'subscribers' allow_messages_from`);
    } catch (error) {
      console.error('Error updating existing users allow_messages_from:', error);
    }

    console.log('Privacy system update completed successfully!');

  } catch (error) {
    console.error('Error updating privacy system:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the script
if (require.main === module) {
  updatePrivacyToSubscribers()
    .then(() => {
      console.log('Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

export { updatePrivacyToSubscribers };
