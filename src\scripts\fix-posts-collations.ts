import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log('Starting migration to fix collation issues for posts and related tables...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
  });

  try {
    // Set target collation
    const targetCollation = "utf8mb4_unicode_ci";

    console.log("Checking current collations...");

    // Check current collations for key tables
    const [columns] = await connection.query(
      `SELECT table_name, column_name, collation_name, data_type
       FROM information_schema.COLUMNS
       WHERE table_schema = ?
       AND table_name IN ('posts', 'users', 'likes', 'comments', 'groups', 'group_members')
       AND data_type IN ('varchar', 'char', 'text', 'enum')
       ORDER BY table_name, column_name`,
      [dbName]
    );

    console.log("Current collations:", columns);

    // Update database collation first
    console.log(`Setting database collation to ${targetCollation}...`);
    await connection.query(
      `ALTER DATABASE ${dbName} CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );

    // List of tables that need to be fixed for the posts query
    const tablesToFix = [
      'users',
      'posts',
      'likes',
      'comments',
      'groups',
      'group_members',
      'friendships',
      'messages'
    ];

    // Convert each table to the target collation
    for (const tableName of tablesToFix) {
      try {
        console.log(`Converting table ${tableName} to ${targetCollation}...`);
        await connection.query(
          `ALTER TABLE ${tableName} CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
        );
        console.log(`✓ Successfully converted ${tableName}`);
      } catch (error) {
        console.log(`⚠ Warning: Could not convert table ${tableName}:`, (error as Error).message);
        // Continue with other tables even if one fails
      }
    }

    // Specifically fix the key columns that are used in joins
    console.log("Fixing specific key columns used in joins...");

    const columnFixes = [
      // Users table
      { table: 'users', column: 'id', type: 'VARCHAR(255)' },
      { table: 'users', column: 'username', type: 'VARCHAR(50)' },
      { table: 'users', column: 'email', type: 'VARCHAR(255)' },

      // Posts table
      { table: 'posts', column: 'id', type: 'VARCHAR(255)' },
      { table: 'posts', column: 'userId', type: 'VARCHAR(255)' },
      { table: 'posts', column: 'pageId', type: 'VARCHAR(255)' },
      { table: 'posts', column: 'groupId', type: 'VARCHAR(255)' },
      { table: 'posts', column: 'sharedPostId', type: 'VARCHAR(255)' },

      // Likes table
      { table: 'likes', column: 'id', type: 'VARCHAR(255)' },
      { table: 'likes', column: 'userId', type: 'VARCHAR(255)' },
      { table: 'likes', column: 'postId', type: 'VARCHAR(255)' },
      { table: 'likes', column: 'commentId', type: 'VARCHAR(255)' },

      // Comments table
      { table: 'comments', column: 'id', type: 'VARCHAR(255)' },
      { table: 'comments', column: 'userId', type: 'VARCHAR(255)' },
      { table: 'comments', column: 'postId', type: 'VARCHAR(255)' },
      { table: 'comments', column: 'parentId', type: 'VARCHAR(255)' },

      // Groups table
      { table: 'groups', column: 'id', type: 'VARCHAR(255)' },
      { table: 'groups', column: 'creatorId', type: 'VARCHAR(255)' },

      // Group members table
      { table: 'group_members', column: 'id', type: 'VARCHAR(255)' },
      { table: 'group_members', column: 'userId', type: 'VARCHAR(255)' },
      { table: 'group_members', column: 'groupId', type: 'VARCHAR(255)' },
    ];

    for (const fix of columnFixes) {
      try {
        console.log(`Fixing ${fix.table}.${fix.column}...`);
        await connection.query(
          `ALTER TABLE ${fix.table} MODIFY ${fix.column} ${fix.type} CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
        );
        console.log(`✓ Fixed ${fix.table}.${fix.column}`);
      } catch (error) {
        console.log(`⚠ Warning: Could not fix ${fix.table}.${fix.column}:`, (error as Error).message);
        // Continue with other columns even if one fails
      }
    }

    // Verify the fix by checking collations again
    console.log("Verifying collations after fix...");
    const [verifyColumns] = await connection.query(
      `SELECT table_name, column_name, collation_name, data_type
       FROM information_schema.COLUMNS
       WHERE table_schema = ?
       AND table_name IN ('posts', 'users', 'likes', 'comments')
       AND data_type IN ('varchar', 'char', 'text', 'enum')
       AND collation_name != ?
       ORDER BY table_name, column_name`,
      [dbName, targetCollation]
    );

    if (Array.isArray(verifyColumns) && verifyColumns.length > 0) {
      console.log("⚠ Warning: Some columns still have different collations:", verifyColumns);
    } else {
      console.log("✓ All key columns now have the correct collation!");
    }

    console.log("Posts collation fix completed successfully!");
  } catch (error) {
    console.error("Error during migration:", error);
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
