import { db } from '../lib/db';
import { subscriptionPlans } from '../lib/db/schema';

async function createSubscriptionPlans() {
  try {
    console.log('🚀 Creating subscription plans...');

    // Check if plans already exist
    const existingPlans = await db.query.subscriptionPlans.findMany();
    
    if (existingPlans.length > 0) {
      console.log(`Found ${existingPlans.length} existing plans. Skipping creation.`);
      return;
    }

    const plans = [
      {
        id: 'free-plan',
        name: 'free',
        displayName: 'Free',
        description: 'Basic features with limited access - perfect for getting started',
        price: '0.00',
        currency: 'USD',
        billingCycle: 'monthly' as const,
        features: [
          'Create up to 10 posts per month',
          'Join unlimited groups',
          'Basic messaging',
          '100MB storage',
          'Community support'
        ],
        maxPosts: 10,
        maxStorage: 100,
        maxGroups: -1,
        canCreateFanPages: false,
        canCreateStores: false,
        canMonetizeBlogs: false,
        prioritySupport: false,
        isActive: true,
        sortOrder: 1
      },
      {
        id: 'basic-plan',
        name: 'basic',
        displayName: 'Basic',
        description: 'Perfect for casual users who want more features and flexibility',
        price: '9.99',
        currency: 'USD',
        billingCycle: 'monthly' as const,
        features: [
          'Create up to 100 posts per month',
          'Create up to 3 groups',
          'Advanced messaging',
          '1GB storage',
          'Email support',
          'Ad-free experience'
        ],
        maxPosts: 100,
        maxStorage: 1024,
        maxGroups: 3,
        canCreateFanPages: false,
        canCreateStores: false,
        canMonetizeBlogs: false,
        prioritySupport: false,
        isActive: true,
        sortOrder: 2
      },
      {
        id: 'premium-plan',
        name: 'premium',
        displayName: 'Premium',
        description: 'Advanced features for power users and content creators',
        price: '19.99',
        currency: 'USD',
        billingCycle: 'monthly' as const,
        features: [
          'Unlimited posts',
          'Create unlimited groups',
          'Premium messaging features',
          '10GB storage',
          'Create fan pages',
          'Blog monetization',
          'Priority support',
          'Advanced analytics'
        ],
        maxPosts: -1,
        maxStorage: 10240,
        maxGroups: -1,
        canCreateFanPages: true,
        canCreateStores: false,
        canMonetizeBlogs: true,
        prioritySupport: true,
        isActive: true,
        sortOrder: 3
      },
      {
        id: 'pro-plan',
        name: 'pro',
        displayName: 'Pro',
        description: 'Complete solution for businesses and professional creators',
        price: '49.99',
        currency: 'USD',
        billingCycle: 'monthly' as const,
        features: [
          'Everything in Premium',
          'Unlimited storage',
          'Create marketplace stores',
          'Advanced monetization tools',
          'White-label options',
          'API access',
          'Dedicated support',
          'Custom integrations'
        ],
        maxPosts: -1,
        maxStorage: -1,
        maxGroups: -1,
        canCreateFanPages: true,
        canCreateStores: true,
        canMonetizeBlogs: true,
        prioritySupport: true,
        isActive: true,
        sortOrder: 4
      }
    ];

    // Insert plans
    for (const plan of plans) {
      try {
        await db.insert(subscriptionPlans).values(plan);
        console.log(`✅ Created subscription plan: ${plan.displayName}`);
      } catch (error) {
        console.log(`ℹ️ Plan ${plan.displayName} already exists or error:`, error);
      }
    }

    console.log('🎉 Subscription plans creation completed!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error creating subscription plans:', error);
    process.exit(1);
  }
}

createSubscriptionPlans();
