"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Spinner } from "@/components/ui/Spinner";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/Tabs";

import { AddUserModal } from "@/components/admin/users/AddUserModal";
import Image from "next/image";
import {
  MagnifyingGlassIcon,
  CheckCircleIcon,
  TrashIcon,
  UserPlusIcon,
  ArrowPathIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  WalletIcon,
  EyeIcon,
  NoSymbolIcon,
} from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";

interface User {
  id: string;
  name: string;
  email: string;
  image: string | null;
  role: string;
  isAdmin: boolean;
  isVerified: boolean;
  isBanned: boolean;
  createdAt: string;
  generalBalance: string;
  earningBalance: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export default function AdminUsersPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [users, setUsers] = useState<User[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pagination, setPagination] = useState<PaginationData | null>(null);

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Function to fetch users from the API
  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', currentPage.toString());
      params.append('limit', '10');

      if (searchQuery) {
        params.append('search', searchQuery);
      }

      // Only send filter for admins, as we'll handle verified and banned client-side
      if (activeTab === 'admins') {
        params.append('filter', activeTab);
      }

      try {
        const response = await fetch(`/api/admin/users?${params.toString()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }

        const data = await response.json();
        let filteredUsers = data.users;

        // Apply client-side filtering for verified and banned users
        if (activeTab === 'verified') {
          filteredUsers = filteredUsers.filter((user: User) => user.isVerified);
        } else if (activeTab === 'banned') {
          filteredUsers = filteredUsers.filter((user: User) => user.isBanned);
        }

        setUsers(filteredUsers);
        setPagination({
          ...data.pagination,
          total: filteredUsers.length,
          totalPages: Math.ceil(filteredUsers.length / 10)
        });
        setTotalPages(Math.ceil(filteredUsers.length / 10));
      } catch (apiError) {
        console.error('API Error:', apiError);
        // Fallback to mock data if API fails
        const mockUsers = Array.from({ length: 20 }, (_, i) => ({
          id: `user-${i + 1}`,
          name: `User ${i + 1}`,
          email: `user${i + 1}@example.com`,
          image: i % 3 === 0 ? `https://i.pravatar.cc/150?u=user${i + 1}` : null,
          role: i % 10 === 0 ? "admin" : "user",
          isAdmin: i % 10 === 0,
          isVerified: i % 4 === 0,
          isBanned: i % 20 === 0,
          createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
          generalBalance: (Math.random() * 10000).toFixed(2),
          earningBalance: (Math.random() * 5000).toFixed(2),
        }));

        // Apply client-side filtering for mock data too
        let filteredMockUsers = mockUsers;
        if (activeTab === 'admins') {
          filteredMockUsers = mockUsers.filter(user => user.isAdmin);
        } else if (activeTab === 'verified') {
          filteredMockUsers = mockUsers.filter(user => user.isVerified);
        } else if (activeTab === 'banned') {
          filteredMockUsers = mockUsers.filter(user => user.isBanned);
        }

        setUsers(filteredMockUsers);
        setPagination({
          total: filteredMockUsers.length,
          page: currentPage,
          limit: 10,
          totalPages: Math.ceil(filteredMockUsers.length / 10),
        });
        setTotalPages(Math.ceil(filteredMockUsers.length / 10));
      }
    } catch (error) {
      console.error('Error in fetchUsers:', error);
      toast.error('Failed to load users');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch users when page, tab, or search query changes
  useEffect(() => {
    fetchUsers();
  }, [currentPage, activeTab]);

  // Debounce search to avoid too many requests
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentPage === 1) {
        fetchUsers();
      } else {
        setCurrentPage(1); // This will trigger fetchUsers via the dependency
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const handleAddUser = async (userData: any) => {
    setIsActionLoading(true);
    try {
      try {
        const response = await fetch('/api/admin/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(userData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to create user');
        }

        // Refresh the user list
        await fetchUsers();
      } catch (apiError) {
        console.error('API Error:', apiError);
        // Fallback to mock data if API fails
        const newUser = {
          id: `user-${Date.now()}`,
          name: userData.name,
          email: userData.email,
          role: userData.role || 'user',
          isAdmin: userData.isAdmin || false,
          isVerified: false,
          isBanned: false,
          image: null,
          createdAt: new Date().toISOString(),
          generalBalance: "0.00",
          earningBalance: "0.00",
        };

        setUsers(prevUsers => [newUser, ...prevUsers]);
      }

      toast.success('User created successfully');
    } catch (error) {
      console.error('Error creating user:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create user');
      throw error;
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    const reason = prompt('Enter reason for PERMANENTLY deleting this user:');
    if (!reason || !reason.trim()) {
      toast.error('Deletion reason is required');
      return;
    }

    if (!window.confirm('🚨 CRITICAL WARNING: Are you sure you want to PERMANENTLY DELETE this user?\n\n⚠️ This will PERMANENTLY remove:\n• User profile and all personal data\n• All posts, comments, and likes\n• Wallet and transaction history\n• All friendships and messages\n• Group memberships and activities\n• ALL related data from the database\n\n❌ THIS ACTION CANNOT BE UNDONE!\n\nClick OK only if you are absolutely certain.')) {
      return;
    }

    const confirmText = prompt('⚠️ FINAL CONFIRMATION\n\nTo proceed with PERMANENT deletion, type exactly: DELETE PERMANENTLY');
    if (confirmText !== 'DELETE PERMANENTLY') {
      toast.error('Confirmation text does not match. Deletion cancelled.');
      return;
    }

    setIsActionLoading(true);
    try {
      const response = await fetch(`/api/admin/users/${userId}/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: reason.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to delete user');
      }

      // Remove the user from the local state
      setUsers((prevUsers) => prevUsers.filter((user) => user.id !== userId));
      toast.success('User permanently deleted successfully');
    } catch (error: any) {
      console.error('Error deleting user:', error);
      toast.error(error.message || 'Failed to delete user');
    } finally {
      setIsActionLoading(false);
    }
  };

  if (isLoading && users.length === 0) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage users, roles, and permissions
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button onClick={() => setIsAddModalOpen(true)} disabled={isActionLoading}>
            <UserPlusIcon className="mr-2 h-5 w-5" />
            Add User
          </Button>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="border-b border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all">All Users</TabsTrigger>
                <TabsTrigger value="admins">Admins</TabsTrigger>
                <TabsTrigger value="verified">Verified</TabsTrigger>
                <TabsTrigger value="banned">Banned</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="mt-4 sm:mt-0">
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <MagnifyingGlassIcon
                    className="h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                </div>
                <Input
                  type="text"
                  placeholder="Search users..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {isLoading && (
            <div className="flex justify-center py-4">
              <Spinner size="md" />
            </div>
          )}
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  User
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Role
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Status
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Wallet Balance
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Created
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {users.map((user) => (
                <tr key={user.id}>
                  <td className="whitespace-nowrap px-6 py-4">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex-shrink-0">
                        {user.image ? (
                          <Image
                            src={user.image}
                            alt={user.name}
                            width={40}
                            height={40}
                            className="h-10 w-10 rounded-full"
                          />
                        ) : (
                          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                            <span className="text-sm font-medium text-gray-500">
                              {user.name.charAt(0)}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {user.name}
                        </div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                        user.isAdmin
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {user.role}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4">
                    <div className="flex flex-col space-y-1">
                      {user.isVerified && (
                        <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                          <CheckCircleIcon className="mr-1 h-3 w-3" />
                          Verified
                        </span>
                      )}
                      {user.isBanned && (
                        <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                          <NoSymbolIcon className="mr-1 h-3 w-3" />
                          Banned
                        </span>
                      )}
                      {!user.isVerified && !user.isBanned && (
                        <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                          Active
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4">
                    <div className="text-sm text-gray-900">
                      <div className="flex items-center space-x-2 mb-2">
                        <WalletIcon className="h-4 w-4 text-gray-400" />
                        <span className="text-xs font-medium text-gray-600">Wallet Balance</span>
                      </div>
                      <div className="flex flex-col space-y-1 ml-6">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">General:</span>
                          <span className="font-medium text-green-600">৳{parseFloat(user.generalBalance || "0").toLocaleString()}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">Earning:</span>
                          <span className="font-medium text-blue-600">৳{parseFloat(user.earningBalance || "0").toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {new Date(user.createdAt).toLocaleDateString()}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => router.push(`/admin/users/${user.id}`)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="View user details"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user.id)}
                        className="text-red-600 hover:text-red-900"
                        disabled={isActionLoading}
                        title="Delete user"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
          <div className="flex flex-1 justify-between sm:hidden">
            <Button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              Previous
            </Button>
            <Button
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
              variant="outline"
              size="sm"
            >
              Next
            </Button>
          </div>
          <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{" "}
                <span className="font-medium">
                  {(currentPage - 1) * 10 + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(currentPage * 10, pagination?.total || 0)}
                </span>{" "}
                of <span className="font-medium">{pagination?.total || 0}</span>{" "}
                results
              </p>
            </div>
            <div>
              <nav
                className="isolate inline-flex -space-x-px rounded-md shadow-sm"
                aria-label="Pagination"
              >
                <button
                  onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center rounded-l-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-20 disabled:opacity-50"
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
                </button>
                {pagination && Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map(
                  (page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center border px-4 py-2 text-sm font-medium ${
                        page === currentPage
                          ? "z-10 border-blue-500 bg-blue-50 text-blue-600"
                          : "border-gray-300 bg-white text-gray-500 hover:bg-gray-50"
                      }`}
                    >
                      {page}
                    </button>
                  )
                )}
                <button
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center rounded-r-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-20 disabled:opacity-50"
                >
                  <span className="sr-only">Next</span>
                  <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Add User Modal */}
      <AddUserModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddUser}
      />
    </AdminLayout>
  );
}
