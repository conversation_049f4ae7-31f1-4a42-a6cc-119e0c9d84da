import { exec } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';

const execAsync = promisify(exec);

async function runScript(scriptName: string) {
  try {
    console.log(`Running ${scriptName}...`);
    
    // Get the absolute path to the script
    const scriptPath = path.resolve(__dirname, scriptName);
    
    // Run the script using ts-node
    const { stdout, stderr } = await execAsync(`npx ts-node ${scriptPath}`);
    
    if (stdout) {
      console.log(stdout);
    }
    
    if (stderr) {
      console.error(stderr);
    }
    
    console.log(`${scriptName} completed successfully!`);
    return true;
  } catch (error) {
    console.error(`Error running ${scriptName}:`, error);
    return false;
  }
}

async function main() {
  console.log('Setting up Marketplace module...');
  
  // Run scripts in sequence
  const scripts = [
    'add-marketplace-tables.ts',
    'add-store-settings-table.ts'
  ];
  
  for (const script of scripts) {
    const success = await runScript(script);
    if (!success) {
      console.error(`Failed to run ${script}. Stopping setup.`);
      process.exit(1);
    }
  }
  
  console.log('Marketplace setup completed successfully!');
}

main();
