const mysql2 = require('mysql2/promise');
const { config } = require('dotenv');
const { v4: uuidv4 } = require('uuid');

// Load environment variables
config();

async function updateGatewayTypes() {
  let connection: any = null;

  try {
    console.log('🚀 Updating payment gateway types...');

    // Create database connection
    connection = await mysql2.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'hifnf',
    });

    console.log('✅ Connected to database');

    // Update the payment_gateways table to include new types
    await connection.query(`
      ALTER TABLE payment_gateways
      MODIFY COLUMN type ENUM(
        'stripe',
        'paypal',
        'sslcommerz',
        'bkash',
        'nagad',
        'rocket',
        'bank',
        'uddoktapay',
        'manual'
      ) NOT NULL
    `);

    console.log('✅ Updated payment gateway types');

    // Insert new default gateways if they don't exist

    // Check if UddoktaPay gateway exists
    const [uddoktaPayRows] = await connection.query(
      'SELECT id FROM payment_gateways WHERE name = ?',
      ['uddoktapay']
    );

    if ((uddoktaPayRows as any[]).length === 0) {
      await connection.query(`
        INSERT INTO payment_gateways (
          id, name, displayName, type, isActive, config,
          depositFee, depositFixedFee, minDeposit, maxDeposit,
          currency, sortOrder, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        uuidv4(),
        'uddoktapay',
        'UddoktaPay',
        'uddoktapay',
        false,
        JSON.stringify({
          apiKey: '',
          apiUrl: 'https://sandbox.uddoktapay.com/api/checkout-v2',
          storeId: '',
        }),
        '2.50',
        '0.00',
        '10.00',
        '100000.00',
        'BDT',
        5
      ]);
      console.log('✅ Added UddoktaPay gateway');
    }

    // Check if Manual Payment gateway exists
    const [manualRows] = await connection.query(
      'SELECT id FROM payment_gateways WHERE name = ?',
      ['manual']
    );

    if ((manualRows as any[]).length === 0) {
      await connection.query(`
        INSERT INTO payment_gateways (
          id, name, displayName, type, isActive, config,
          depositFee, depositFixedFee, minDeposit, maxDeposit,
          currency, sortOrder, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        uuidv4(),
        'manual',
        'Manual Payment Method',
        'manual',
        false,
        JSON.stringify({
          instructions: 'Please contact support for manual payment instructions.',
          accountDetails: 'Bank account details will be provided upon request.',
        }),
        '0.00',
        '0.00',
        '1.00',
        '10000.00',
        'USD',
        6
      ]);
      console.log('✅ Added Manual Payment gateway');
    }

    console.log('🎉 Payment gateway types updated successfully!');

  } catch (error) {
    console.error('❌ Error updating gateway types:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('✅ Database connection closed');
    }
  }
}

// Run the update if this file is executed directly
if (require.main === module) {
  updateGatewayTypes()
    .then(() => {
      console.log('✅ Update completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Update failed:', error);
      process.exit(1);
    });
}

module.exports = { updateGatewayTypes };
