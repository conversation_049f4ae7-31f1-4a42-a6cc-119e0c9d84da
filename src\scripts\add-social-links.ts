import mysql from 'mysql2/promise';
import { config } from 'dotenv';

// Load environment variables
config();

async function addSocialLinksColumns() {
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST || 'localhost',
    user: process.env.DATABASE_USERNAME || 'root',
    password: process.env.DATABASE_PASSWORD || '',
    database: process.env.DATABASE_NAME || 'hifnf',
    port: parseInt(process.env.DATABASE_PORT || '3306'),
  });

  try {
    console.log('Adding social links columns to users table...');

    // Add social links columns to users table
    const socialLinksColumns = [
      'website VARCHAR(255)',
      'facebook VARCHAR(255)',
      'twitter VARCHAR(255)',
      'instagram VARCHAR(255)',
      'linkedin VARCHAR(255)',
      'youtube VARCHAR(255)'
    ];

    for (const column of socialLinksColumns) {
      const columnName = column.split(' ')[0];

      try {
        // Check if column exists
        const [rows] = await connection.execute(
          `SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
           WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users' AND COLUMN_NAME = ?`,
          [process.env.DATABASE_NAME || 'hifnf', columnName]
        );

        if ((rows as any[]).length === 0) {
          // Column doesn't exist, add it
          await connection.execute(`ALTER TABLE users ADD COLUMN ${column}`);
          console.log(`✅ Added column: ${columnName}`);
        } else {
          console.log(`⚠️  Column ${columnName} already exists, skipping...`);
        }
      } catch (error) {
        console.error(`❌ Error adding column ${columnName}:`, error);
      }
    }

    console.log('✅ Social links columns migration completed!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await connection.end();
  }
}

// Run the migration
addSocialLinksColumns().catch(console.error);
