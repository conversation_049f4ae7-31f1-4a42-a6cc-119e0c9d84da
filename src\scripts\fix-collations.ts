import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log('Starting migration to fix collation issues...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
  });

  try {
    // Check database collation
    console.log("Checking database collation...");
    const [dbResult] = await connection.query(
      `SELECT default_character_set_name, default_collation_name
       FROM information_schema.SCHEMATA
       WHERE schema_name = ?`,
      [dbName]
    );
    console.log("Database collation:", dbResult);

    // Get all tables
    console.log("Getting all tables...");
    const [tables] = await connection.query(
      `SELECT table_name, table_collation
       FROM information_schema.TABLES
       WHERE table_schema = ?`,
      [dbName]
    );
    console.log("Tables and their collations:", tables);

    // Get all columns with their collations
    console.log("Getting all columns with their collations...");
    const [columns] = await connection.query(
      `SELECT table_name, column_name, collation_name, data_type
       FROM information_schema.COLUMNS
       WHERE table_schema = ? AND data_type IN ('varchar', 'char', 'text', 'enum')`,
      [dbName]
    );
    console.log("Columns and their collations:", columns);

    // Set target collation
    const targetCollation = "utf8mb4_unicode_ci";

    // Update database collation
    console.log(`Setting database collation to ${targetCollation}...`);
    await connection.query(
      `ALTER DATABASE ${dbName} CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );

    // Update table collations
    console.log("Updating table collations...");
    for (const table of Array.isArray(tables) ? tables : []) {
      // Use type assertion to handle the dynamic properties
      const typedTable = table as { TABLE_COLLATION: string, TABLE_NAME: string };
      if (typedTable.TABLE_COLLATION !== targetCollation && typedTable.TABLE_NAME) {
        console.log(`Updating collation for table ${typedTable.TABLE_NAME}...`);
        await connection.query(
          `ALTER TABLE ${typedTable.TABLE_NAME} CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
        );
      }
    }

    // Specifically fix the users and group_members tables
    console.log("Specifically fixing users and group_members tables...");
    await connection.query(
      `ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );
    await connection.query(
      `ALTER TABLE group_members CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );

    // Fix specific columns that might be causing issues
    console.log("Fixing specific columns...");
    await connection.query(
      `ALTER TABLE users MODIFY id VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );
    await connection.query(
      `ALTER TABLE group_members MODIFY userId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );
    await connection.query(
      `ALTER TABLE group_members MODIFY groupId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );

    console.log("Collation update completed successfully!");
  } catch (error) {
    console.error("Error during migration:", error);
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
