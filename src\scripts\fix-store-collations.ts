import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log('Starting migration to fix store collation issues...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
    multipleStatements: true
  });

  try {
    // Set target collation to match users table (utf8mb4_unicode_ci)
    const targetCollation = "utf8mb4_unicode_ci";

    console.log("Step 1: Disabling foreign key checks...");
    await connection.query("SET FOREIGN_KEY_CHECKS = 0");

    // Fix store_reviews table first
    console.log("Step 2: Converting store_reviews table...");
    await connection.query(
      `ALTER TABLE store_reviews CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );
    console.log("✓ store_reviews table converted");

    // Fix store_follows table
    console.log("Step 3: Converting store_follows table...");
    await connection.query(
      `ALTER TABLE store_follows CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
    );
    console.log("✓ store_follows table converted");

    // Fix any other tables that might have utf8mb4_0900_ai_ci
    const tablesToFix = [
      'accounts',
      'sessions', 
      'friendships',
      'messages',
      'notifications',
      'pagelikes',
      'verificationtokens'
    ];

    for (const tableName of tablesToFix) {
      try {
        console.log(`Step: Converting ${tableName} table...`);
        await connection.query(
          `ALTER TABLE ${tableName} CONVERT TO CHARACTER SET utf8mb4 COLLATE ${targetCollation}`
        );
        console.log(`✓ ${tableName} table converted`);
      } catch (error) {
        console.log(`⚠ Warning: Could not convert table ${tableName}:`, (error as Error).message);
        // Continue with other tables even if one fails
      }
    }

    console.log("Step: Re-enabling foreign key checks...");
    await connection.query("SET FOREIGN_KEY_CHECKS = 1");

    console.log("✅ Collation update completed successfully!");
    
    // Verify the fix by checking collations
    console.log("Verifying collations...");
    const [storeReviewsCollation] = await connection.query(
      `SELECT TABLE_COLLATION FROM information_schema.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'store_reviews'`,
      [dbName]
    );
    const [usersCollation] = await connection.query(
      `SELECT TABLE_COLLATION FROM information_schema.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'users'`,
      [dbName]
    );
    
    console.log("store_reviews collation:", storeReviewsCollation);
    console.log("users collation:", usersCollation);

  } catch (error) {
    console.error("Error during migration:", error);
    
    // Re-enable foreign key checks in case of error
    try {
      await connection.query("SET FOREIGN_KEY_CHECKS = 1");
    } catch (e) {
      console.error("Failed to re-enable foreign key checks:", e);
    }
    
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
