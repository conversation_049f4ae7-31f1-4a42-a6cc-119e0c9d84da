import { db } from "@/lib/db";

async function addUserStatusColumns() {
  try {
    console.log("Adding user status columns...");

    // Add status enum column
    try {
      await db.execute(`
        ALTER TABLE users 
        ADD COLUMN status ENUM('active', 'disabled', 'suspended', 'deleted') DEFAULT 'active' NOT NULL
      `);
      console.log('✅ Added status column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ status column already exists');
      } else {
        console.error('❌ Error adding status column:', error.message);
      }
    }

    // Add is_active column
    try {
      await db.execute(`
        ALTER TABLE users 
        ADD COLUMN is_active BOOLEAN DEFAULT TRUE NOT NULL
      `);
      console.log('✅ Added is_active column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ is_active column already exists');
      } else {
        console.error('❌ Error adding is_active column:', error.message);
      }
    }

    // Add suspended_at column
    try {
      await db.execute(`
        ALTER TABLE users 
        ADD COLUMN suspended_at TIMESTAMP NULL
      `);
      console.log('✅ Added suspended_at column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ suspended_at column already exists');
      } else {
        console.error('❌ Error adding suspended_at column:', error.message);
      }
    }

    // Add suspended_reason column
    try {
      await db.execute(`
        ALTER TABLE users 
        ADD COLUMN suspended_reason TEXT NULL
      `);
      console.log('✅ Added suspended_reason column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ suspended_reason column already exists');
      } else {
        console.error('❌ Error adding suspended_reason column:', error.message);
      }
    }

    // Add suspended_by column
    try {
      await db.execute(`
        ALTER TABLE users 
        ADD COLUMN suspended_by VARCHAR(255) NULL
      `);
      console.log('✅ Added suspended_by column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ suspended_by column already exists');
      } else {
        console.error('❌ Error adding suspended_by column:', error.message);
      }
    }

    // Add deleted_at column
    try {
      await db.execute(`
        ALTER TABLE users 
        ADD COLUMN deleted_at TIMESTAMP NULL
      `);
      console.log('✅ Added deleted_at column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ deleted_at column already exists');
      } else {
        console.error('❌ Error adding deleted_at column:', error.message);
      }
    }

    // Add deleted_reason column
    try {
      await db.execute(`
        ALTER TABLE users 
        ADD COLUMN deleted_reason TEXT NULL
      `);
      console.log('✅ Added deleted_reason column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ deleted_reason column already exists');
      } else {
        console.error('❌ Error adding deleted_reason column:', error.message);
      }
    }

    // Add deleted_by column
    try {
      await db.execute(`
        ALTER TABLE users 
        ADD COLUMN deleted_by VARCHAR(255) NULL
      `);
      console.log('✅ Added deleted_by column');
    } catch (error: any) {
      if (error.message.includes('Duplicate column name')) {
        console.log('ℹ️ deleted_by column already exists');
      } else {
        console.error('❌ Error adding deleted_by column:', error.message);
      }
    }

    console.log("✅ User status columns migration completed!");
  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  }
}

// Run the migration
addUserStatusColumns()
  .then(() => {
    console.log("Migration completed successfully!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Migration failed:", error);
    process.exit(1);
  });
