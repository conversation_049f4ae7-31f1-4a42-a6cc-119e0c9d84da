import { db } from '../lib/db';
import { paymentGateways } from '../lib/db/schema';
import { eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';

async function activatePaymentGateways() {
  try {
    console.log('🚀 Activating payment gateways...');

    // Check if any gateways exist
    const existingGateways = await db.query.paymentGateways.findMany();
    
    if (existingGateways.length === 0) {
      console.log('No payment gateways found. Creating sample gateways...');
      
      // Create sample payment gateways
      const sampleGateways = [
        {
          id: uuidv4(),
          name: 'stripe',
          displayName: 'Stripe',
          type: 'stripe' as const,
          isActive: true,
          config: { 
            publishableKey: 'pk_test_sample', 
            secretKey: 'sk_test_sample',
            webhookSecret: 'whsec_sample'
          },
          depositFee: '2.50',
          depositFixedFee: '0.30',
          minDeposit: '10.00',
          maxDeposit: '10000.00',
          currency: 'USD',
          sortOrder: 1,
        },
        {
          id: uuidv4(),
          name: 'paypal',
          displayName: 'PayPal',
          type: 'paypal' as const,
          isActive: true,
          config: { 
            clientId: 'sample_client_id', 
            clientSecret: 'sample_client_secret',
            mode: 'sandbox'
          },
          depositFee: '3.00',
          depositFixedFee: '0.50',
          minDeposit: '5.00',
          maxDeposit: '5000.00',
          currency: 'USD',
          sortOrder: 2,
        },
        {
          id: uuidv4(),
          name: 'uddoktapay',
          displayName: 'UddoktaPay',
          type: 'uddoktapay' as const,
          isActive: true,
          config: { 
            apiKey: 'sample_api_key',
            storeId: 'sample_store_id',
            apiUrl: 'https://sandbox.uddoktapay.com/api/checkout-v2'
          },
          depositFee: '2.00',
          depositFixedFee: '0.00',
          minDeposit: '10.00',
          maxDeposit: '100000.00',
          currency: 'BDT',
          sortOrder: 3,
        },
        {
          id: uuidv4(),
          name: 'manual',
          displayName: 'Manual Payment',
          type: 'manual' as const,
          isActive: true,
          config: { 
            instructions: 'Please contact admin for manual payment instructions.',
            accountDetails: 'Bank account details will be provided upon request.'
          },
          depositFee: '0.00',
          depositFixedFee: '0.00',
          minDeposit: '1.00',
          maxDeposit: '50000.00',
          currency: 'USD',
          sortOrder: 4,
        }
      ];

      // Insert payment gateways
      for (const gateway of sampleGateways) {
        try {
          await db.insert(paymentGateways).values(gateway);
          console.log(`✅ Created payment gateway: ${gateway.displayName}`);
        } catch (error) {
          console.log(`ℹ️ Payment gateway ${gateway.displayName} already exists or error:`, error);
        }
      }
    } else {
      console.log(`Found ${existingGateways.length} existing payment gateways. Activating them...`);
      
      // Activate all existing gateways for testing
      for (const gateway of existingGateways) {
        try {
          await db.update(paymentGateways)
            .set({ isActive: true })
            .where(eq(paymentGateways.id, gateway.id));
          console.log(`✅ Activated payment gateway: ${gateway.displayName}`);
        } catch (error) {
          console.log(`❌ Failed to activate ${gateway.displayName}:`, error);
        }
      }
    }

    console.log('🎉 Payment gateways activation completed!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error activating payment gateways:', error);
    process.exit(1);
  }
}

activatePaymentGateways();
