"use client";

import Link from "next/link";
import Image from "next/image";
import { UserIcon } from "@heroicons/react/24/outline";

interface User {
  id: string;
  name: string;
  username: string;
  email: string;
  image: string | null;
  bio: string | null;
}

interface UserCardProps {
  user: User;
}

export function UserCard({ user }: UserCardProps) {
  return (
    <Link href={`/user/${user.username}`}>
      <div className="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {user.image ? (
              <Image
                src={user.image}
                alt={user.name}
                width={48}
                height={48}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                <UserIcon className="w-6 h-6 text-gray-500" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 truncate">
              {user.name}
            </h3>
            <p className="text-sm text-gray-500 truncate">
              @{user.username}
            </p>
            {user.bio && (
              <p className="text-xs text-gray-400 mt-1 line-clamp-2">
                {user.bio}
              </p>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}
