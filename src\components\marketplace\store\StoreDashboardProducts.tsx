"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { OptimizedImage } from "@/components/ui/OptimizedImage";
import { Button } from "@/components/ui/Button";
import { formatDistanceToNow } from "date-fns";
import {
  PencilIcon,
  TrashIcon,
  ShoppingBagIcon,
  EyeIcon,
  PlusIcon
} from "@heroicons/react/24/outline";

interface StoreDashboardProductsProps {
  products: {
    id: string;
    title: string;
    price: number;
    condition: string;
    photos: string[] | null;
    viewCount: number | null;
    createdAt: Date;
  }[];
  hasMoreProducts: boolean;
}

export function StoreDashboardProducts({
  products,
  hasMoreProducts
}: StoreDashboardProductsProps) {
  const router = useRouter();
  const [deletingProductId, setDeletingProductId] = useState<string | null>(null);

  // Format condition for display
  const formatCondition = (condition: string) => {
    return condition.replace('_', ' ').replace(/\b\w/g, (c) => c.toUpperCase());
  };

  // Format price to currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price); // Price is already in correct format
  };

  // Handle delete product
  const deleteProduct = async (productId: string) => {
    if (!confirm("Are you sure you want to delete this product?")) {
      return;
    }

    try {
      setDeletingProductId(productId);

      const response = await fetch(`/api/marketplace/products/${productId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete product");
      }

      // Refresh the page to show updated product list
      router.refresh();
    } catch (error) {
      console.error("Error deleting product:", error);
      alert("Failed to delete product. Please try again.");
    } finally {
      setDeletingProductId(null);
    }
  };

  return (
    <div className="overflow-hidden rounded-lg bg-white shadow hover:shadow-md transition-all duration-300">
      <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
        <h3 className="text-lg font-medium text-gray-900">Your Products</h3>
        <Link href="/marketplace/product/create">
          <Button size="sm">
            <PlusIcon className="mr-1 h-4 w-4" />
            Add Product
          </Button>
        </Link>
      </div>

      {products.length > 0 ? (
        <div className="divide-y divide-gray-200">
          {products.map((product) => (
            <div key={product.id} className="flex items-center p-4 hover:bg-gray-50 transition-colors duration-200">
              <div className="mr-4 h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                {product.photos && product.photos.length > 0 ? (
                  <OptimizedImage
                    src={product.photos[0]}
                    alt={product.title}
                    width={64}
                    height={64}
                    className="h-full w-full object-cover"
                    customPlaceholder="blur"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-gray-100">
                    <span className="text-xs text-gray-400">No image</span>
                  </div>
                )}
              </div>

              <div className="flex flex-1 flex-col">
                <div className="flex justify-between">
                  <Link
                    href={`/marketplace/product/${product.id}`}
                    className="font-medium text-gray-900 hover:text-blue-600"
                  >
                    {product.title}
                  </Link>
                  <p className="text-sm font-medium text-gray-900">{formatPrice(product.price)}</p>
                </div>

                <div className="mt-1 flex items-center text-sm text-gray-500">
                  <span className="mr-2 rounded-full bg-gray-100 px-2 py-0.5 text-xs">
                    {formatCondition(product.condition)}
                  </span>
                  <span className="flex items-center text-xs">
                    <EyeIcon className="mr-1 h-3 w-3" />
                    {product.viewCount || 0} views
                  </span>
                  <span className="ml-2 text-xs">
                    {formatDistanceToNow(new Date(product.createdAt), { addSuffix: true })}
                  </span>
                </div>
              </div>

              <div className="ml-4 flex">
                <Link href={`/marketplace/product/${product.id}/edit`}>
                  <Button size="sm" variant="outline" className="mr-2">
                    <PencilIcon className="h-4 w-4" />
                  </Button>
                </Link>
                <Button
                  size="sm"
                  variant="outline"
                  className="text-red-600 hover:bg-red-50 hover:text-red-700"
                  onClick={() => deleteProduct(product.id)}
                  disabled={deletingProductId === product.id}
                >
                  {deletingProductId === product.id ? (
                    <span className="h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent"></span>
                  ) : (
                    <TrashIcon className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="p-6 text-center">
          <ShoppingBagIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No products</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating a new product.</p>
          <div className="mt-6">
            <Link href="/marketplace/product/create">
              <Button>
                <PlusIcon className="mr-1 h-5 w-5" />
                Add Product
              </Button>
            </Link>
          </div>
        </div>
      )}

      {products.length > 0 && hasMoreProducts && (
        <div className="border-t border-gray-200 px-6 py-4 text-right">
          <Link
            href="/my-store/products"
            className="text-sm font-medium text-blue-600 hover:text-blue-800"
          >
            View all products
          </Link>
        </div>
      )}
    </div>
  );
}
