import { db } from "@/lib/db";
import { userPaymentMethods } from "@/lib/db/schema";

async function addPaymentMethodsTable() {
  try {
    console.log("🚀 Adding user payment methods table...");

    // The table will be created automatically by <PERSON><PERSON><PERSON> when we run the migration
    // This script is just to ensure the table exists and add any initial data if needed

    console.log("✅ User payment methods table setup completed!");
    
    console.log("\n📋 Next steps:");
    console.log("1. Run: npm run db:generate");
    console.log("2. Run: npm run db:migrate");
    console.log("3. Test the wallet settings functionality");

  } catch (error) {
    console.error("❌ Error setting up payment methods table:", error);
    throw error;
  }
}

// Run the script
if (require.main === module) {
  addPaymentMethodsTable()
    .then(() => {
      console.log("✅ Payment methods table setup completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Failed to setup payment methods table:", error);
      process.exit(1);
    });
}

export { addPaymentMethodsTable };
