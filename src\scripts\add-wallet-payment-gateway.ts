import mysql2 from 'mysql2/promise';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addWalletPaymentGateway() {
  let connection: mysql2.Connection | null = null;

  try {
    console.log('🚀 Adding wallet payment gateway...');

    // Create database connection
    connection = await mysql2.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'hifnf',
    });

    console.log('✅ Connected to database');

    // First, update the payment_gateways table to include 'wallet' type
    await connection.query(`
      ALTER TABLE payment_gateways
      MODIFY COLUMN type ENUM(
        'stripe',
        'paypal',
        'sslcommerz',
        'bkash',
        'nagad',
        'rocket',
        'bank',
        'uddoktapay',
        'manual',
        'wallet'
      ) NOT NULL
    `);

    console.log('✅ Updated payment gateway types to include wallet');

    // Check if wallet gateway already exists
    const [walletRows] = await connection.query(
      'SELECT id FROM payment_gateways WHERE name = ?',
      ['wallet']
    );

    if ((walletRows as any[]).length === 0) {
      // Add wallet payment gateway
      await connection.query(`
        INSERT INTO payment_gateways (
          id, name, displayName, type, isActive, config,
          depositFee, depositFixedFee, minDeposit, maxDeposit,
          currency, sortOrder, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        uuidv4(),
        'wallet',
        'General Wallet Balance',
        'wallet',
        true, // Active by default
        JSON.stringify({
          description: 'Pay using your general wallet balance',
          instantPayment: true,
          requiresBalance: true,
        }),
        '0.00', // No fees for wallet payments
        '0.00',
        '0.01', // Minimum 1 cent
        '999999.99', // High maximum
        'USD',
        1 // High priority in sorting
      ]);
      console.log('✅ Added Wallet Payment gateway');
    } else {
      console.log('ℹ️ Wallet payment gateway already exists');
    }

    console.log('🎉 Wallet payment gateway setup completed successfully!');

  } catch (error) {
    console.error('❌ Error setting up wallet payment gateway:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('✅ Database connection closed');
    }
  }
}

// Run the script
addWalletPaymentGateway();
