import { db } from "@/lib/db";

async function updatePaymentMethodsTable() {
  try {
    console.log("🚀 Updating user payment methods table...");

    // Check if methodType column exists
    const result = await db.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'user_payment_methods'
      AND COLUMN_NAME = 'methodType'
    `);

    if (Array.isArray(result[0]) && result[0].length === 0) {
      // Add methodType column
      await db.execute(`
        ALTER TABLE user_payment_methods
        ADD COLUMN methodType ENUM('payment', 'payout') NOT NULL DEFAULT 'payout'
        AFTER type
      `);
      console.log("✅ methodType column added successfully!");
    } else {
      console.log("ℹ️ methodType column already exists.");
    }

    console.log("✅ User payment methods table updated successfully!");

    console.log("\n📋 Next steps:");
    console.log("1. Test the wallet settings functionality");
    console.log("2. Add payment methods for deposits");
    console.log("3. Add payout methods for withdrawals");

  } catch (error) {
    console.error("❌ Error updating payment methods table:", error);
    throw error;
  }
}

// Run the script
if (require.main === module) {
  updatePaymentMethodsTable()
    .then(() => {
      console.log("✅ Payment methods table update completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Failed to update payment methods table:", error);
      process.exit(1);
    });
}

export { updatePaymentMethodsTable };
