import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function main() {
  console.log('Starting migration to fix collation issues between posts and groups tables...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: dbHost,
    user: dbUser,
    password: dbPassword,
    database: dbName,
    port: dbPort,
  });

  try {
    // Check if there are foreign keys on the posts table
    console.log("Checking for foreign keys on posts table...");
    const [fks] = await connection.query(`
      SELECT CONSTRAINT_NAME
      FROM information_schema.TABLE_CONSTRAINTS
      WHERE TABLE_NAME = 'posts'
      AND CONSTRAINT_TYPE = 'FOREIGN KEY'
      AND TABLE_SCHEMA = ?
    `, [dbName]);

    // Drop foreign keys if they exist
    if (Array.isArray(fks) && fks.length > 0) {
      console.log("Dropping foreign keys on posts table...");
      for (const fk of fks) {
        // @ts-ignore
        await connection.query(`ALTER TABLE posts DROP FOREIGN KEY ${fk.CONSTRAINT_NAME}`);
        // @ts-ignore
        console.log(`Dropped foreign key: ${fk.CONSTRAINT_NAME}`);
      }
    } else {
      console.log("No foreign keys found on posts table.");
    }

    // Update the groupId column in posts table to match the collation of groups.id
    console.log("Updating groupId column in posts table...");
    await connection.query(`
      ALTER TABLE posts 
      MODIFY COLUMN groupId VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
    `);
    console.log("Successfully updated groupId column in posts table.");

    // Re-add foreign key if needed
    // This is commented out because we don't know the exact foreign key definition
    // console.log("Re-adding foreign key constraint...");
    // await connection.query(`
    //   ALTER TABLE posts 
    //   ADD CONSTRAINT posts_groupId_fkey 
    //   FOREIGN KEY (groupId) 
    //   REFERENCES groups(id)
    // `);
    // console.log("Successfully re-added foreign key constraint.");

    console.log("Collation update completed successfully!");
  } catch (error) {
    console.error("Error during migration:", error);
    process.exit(1);
  } finally {
    await connection.end();
    console.log("Migration completed.");
  }
}

main().catch(console.error);
