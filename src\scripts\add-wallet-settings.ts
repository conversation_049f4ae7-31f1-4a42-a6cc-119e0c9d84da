import { db } from '../lib/db';
import { walletSettings } from '../lib/db/schema';
import { v4 as uuidv4 } from 'uuid';

async function addWalletSettings() {
  try {
    console.log('🚀 Adding default wallet settings...');

    const defaultSettings = [
      // Feature toggles
      {
        id: uuidv4(),
        key: 'deposit_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: 'Enable or disable deposit functionality',
        category: 'features',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'withdraw_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: 'Enable or disable withdrawal functionality',
        category: 'features',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'send_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: 'Enable or disable money transfer functionality',
        category: 'features',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'cashout_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: 'Enable or disable cashout through agents',
        category: 'features',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'internal_transfer_enabled',
        value: 'true',
        type: 'boolean' as const,
        description: 'Enable or disable internal wallet transfers',
        category: 'features',
        isSystem: false,
      },

      // Fee settings
      {
        id: uuidv4(),
        key: 'withdraw_fee_percentage',
        value: '2.00',
        type: 'string' as const,
        description: 'Percentage fee charged on withdrawals',
        category: 'fees',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'withdraw_fee_fixed',
        value: '0.00',
        type: 'string' as const,
        description: 'Fixed fee charged on withdrawals',
        category: 'fees',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'withdraw_min_amount',
        value: '10.00',
        type: 'string' as const,
        description: 'Minimum withdrawal amount',
        category: 'fees',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'withdraw_max_amount',
        value: '10000.00',
        type: 'string' as const,
        description: 'Maximum withdrawal amount',
        category: 'fees',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'transfer_fee_percentage',
        value: '0.50',
        type: 'string' as const,
        description: 'Percentage fee charged on transfers',
        category: 'fees',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'transfer_fee_fixed',
        value: '0.00',
        type: 'string' as const,
        description: 'Fixed fee charged on transfers',
        category: 'fees',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'transfer_min_amount',
        value: '1.00',
        type: 'string' as const,
        description: 'Minimum transfer amount',
        category: 'fees',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'transfer_max_amount',
        value: '5000.00',
        type: 'string' as const,
        description: 'Maximum transfer amount',
        category: 'fees',
        isSystem: false,
      },

      // Daily limits
      {
        id: uuidv4(),
        key: 'daily_deposit_limit',
        value: '10000.00',
        type: 'string' as const,
        description: 'Daily deposit limit per user',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'daily_send_limit',
        value: '5000.00',
        type: 'string' as const,
        description: 'Daily send limit per user',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'daily_cashout_limit',
        value: '3000.00',
        type: 'string' as const,
        description: 'Daily cashout limit per user',
        category: 'limits',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'daily_withdraw_limit',
        value: '5000.00',
        type: 'string' as const,
        description: 'Daily withdrawal limit per user',
        category: 'limits',
        isSystem: false,
      },

      // Processing settings
      {
        id: uuidv4(),
        key: 'auto_approve_withdrawals',
        value: 'false',
        type: 'boolean' as const,
        description: 'Automatically approve withdrawal requests',
        category: 'processing',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'withdrawal_processing_time',
        value: '24',
        type: 'string' as const,
        description: 'Expected withdrawal processing time in hours',
        category: 'processing',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'require_pin_for_withdrawals',
        value: 'true',
        type: 'boolean' as const,
        description: 'Require PIN verification for withdrawals',
        category: 'processing',
        isSystem: false,
      },
      {
        id: uuidv4(),
        key: 'require_pin_for_transfers',
        value: 'true',
        type: 'boolean' as const,
        description: 'Require PIN verification for transfers',
        category: 'processing',
        isSystem: false,
      },
    ];

    // Insert settings one by one, skipping if they already exist
    for (const setting of defaultSettings) {
      try {
        await db.insert(walletSettings).values(setting);
        console.log(`✅ Added setting: ${setting.key}`);
      } catch (error: any) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️ Setting already exists: ${setting.key}`);
        } else {
          console.error(`❌ Error adding setting ${setting.key}:`, error.message);
        }
      }
    }

    console.log('✅ Wallet settings setup completed!');
  } catch (error) {
    console.error('❌ Error setting up wallet settings:', error);
    process.exit(1);
  }
}

// Run the setup
addWalletSettings();
